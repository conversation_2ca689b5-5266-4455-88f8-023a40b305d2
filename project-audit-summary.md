# Project Audit & Mapping Summary

## Overview
This audit analyzes the GPAce application structure, identifying file counts, sizes, and dependencies to understand the current state before restructuring.

## File Statistics

### Total Files by Type
- **HTML Files**: 16 files
- **CSS Files**: 46 files  
- **JavaScript Files**: 100 files
- **Total**: 162 files

### Largest Files (Top 10)
1. `grind.html` - 201,061 bytes (196 KB)
2. `grind.css` - 125,643 bytes (123 KB)
3. `ai-researcher.js` - 101,375 bytes (99 KB)
4. `extracted.html` - 92,348 bytes (90 KB)
5. `speech-recognition.js` - 81,174 bytes (79 KB)
6. `googleDriveApi.js` - 72,304 bytes (71 KB)
7. `semester-management.js` - 62,190 bytes (61 KB)
8. `studySpacesManager.js` - 57,162 bytes (56 KB)
9. `landing.html` - 55,202 bytes (54 KB)
10. `test-feedback.js` - 53,354 bytes (52 KB)

### Key Observations

#### HTML Pages (Views vs Components)
**Large Pages (Likely Views)**:
- `grind.html` (201 KB) - Main grinding/study interface
- `extracted.html` (92 KB) - Content extraction interface
- `landing.html` (55 KB) - Landing page
- `academic-details.html` (26 KB) - Academic details management
- `tasks.html` (24 KB) - Task management
- `instant-test-feedback.html` (23 KB) - Test feedback system
- `study-spaces.html` (21 KB) - Study space management
- `workspace.html` (21 KB) - Workspace interface

**Smaller Pages (Potential Components)**:
- `flashcards.html` (16 KB)
- `daily-calendar.html` (10 KB)
- `subject-marks.html` (8 KB)
- `sleep-saboteurs.html` (8 KB)
- `relaxed-mode/index.html` (7 KB)

#### CSS Organization
**Large Stylesheets**:
- `grind.css` (126 KB) - Needs breaking down
- `css/daily-calendar.css` (50 KB)
- `css/workspace.css` (40 KB)
- `css/extracted.css` (40 KB)
- `css/academic-details.css` (27 KB)

#### JavaScript Modules
**Large Scripts (Need Refactoring)**:
- `ai-researcher.js` (101 KB) - AI research functionality
- `speech-recognition.js` (81 KB) - Speech recognition
- `googleDriveApi.js` (72 KB) - Google Drive integration
- `semester-management.js` (62 KB) - Semester management
- `studySpacesManager.js` (57 KB) - Study spaces
- `test-feedback.js` (53 KB) - Test feedback system

## Dependencies Analysis

### External JavaScript Dependencies
- **Bootstrap**: 5.3.2 (UI framework)
- **Chart.js**: Data visualization
- **MathJax**: Mathematical notation
- **PDF.js**: PDF handling
- **FontAwesome**: Icons
- **Firebase**: 10.7.1 (Backend services)
- **ES Module Shims**: Module loading

### External CSS Dependencies
- **Bootstrap**: 5.3.2 + Bootstrap Icons
- **FontAwesome**: 6.5.1
- **Google Fonts**: Inter, Roboto
- **Quill.js**: Rich text editor styling

### Internal Dependencies
- **32 JavaScript modules** referenced across HTML files
- **36 CSS files** linked across pages
- Heavy cross-dependencies between modules

## Recommendations for Restructuring

### 1. Views vs Components Split
**Views** (move to `/src/views/`):
- grind.html, landing.html, academic-details.html
- tasks.html, study-spaces.html, workspace.html
- instant-test-feedback.html, extracted.html

**Components** (move to `/src/components/`):
- flashcards.html, daily-calendar.html
- subject-marks.html, sleep-saboteurs.html
- priority-calculator.html, settings.html

### 2. CSS Restructuring
- Break down `grind.css` (126 KB) into component-specific files
- Consolidate similar stylesheets in `/src/styles/`
- Create shared utility CSS files

### 3. JavaScript Modularization
- Split large files (>50 KB) into smaller, focused modules
- Group related functionality (AI, Firebase, UI utilities)
- Implement proper module structure in `/src/scripts/`

### 4. Asset Organization
- Move to `/src/assets/` with subdirectories:
  - `/images/`, `/audio/`, `/fonts/`
- Organize by feature rather than file type

## Next Steps
1. Create `/src` directory structure
2. Migrate files using find/mv commands
3. Update file references using sed/regex patterns
4. Test functionality after migration
5. Implement module bundling if needed
