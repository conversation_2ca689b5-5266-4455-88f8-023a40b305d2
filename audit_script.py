#!/usr/bin/env python3
import os
import glob
import re
from pathlib import Path

def create_audit():
    """Create audit files for the project"""
    
    # Find all HTML, CSS, and JS files with their sizes
    print("Creating file audit...")
    with open('audit.tsv', 'w') as f:
        f.write("File\tSize\n")
        for pattern in ['**/*.html', '**/*.css', '**/*.js']:
            for file_path in glob.glob(pattern, recursive=True):
                if os.path.isfile(file_path):
                    size = os.path.getsize(file_path)
                    f.write(f"{file_path}\t{size} bytes\n")
    
    # Extract JavaScript dependencies
    print("Extracting JavaScript dependencies...")
    js_deps = set()
    for html_file in glob.glob('**/*.html', recursive=True):
        try:
            with open(html_file, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
                # Find script tags with src attributes
                script_matches = re.findall(r'<script[^>]+src=["\']([^"\']+)["\']', content, re.IGNORECASE)
                js_deps.update(script_matches)
        except Exception as e:
            print(f"Error reading {html_file}: {e}")
    
    with open('js_deps.txt', 'w') as f:
        for dep in sorted(js_deps):
            f.write(f"{dep}\n")
    
    # Extract CSS dependencies
    print("Extracting CSS dependencies...")
    css_deps = set()
    for html_file in glob.glob('**/*.html', recursive=True):
        try:
            with open(html_file, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
                # Find link tags with href attributes (CSS files)
                link_matches = re.findall(r'<link[^>]+href=["\']([^"\']+)["\']', content, re.IGNORECASE)
                css_deps.update(link_matches)
        except Exception as e:
            print(f"Error reading {html_file}: {e}")
    
    with open('css_deps.txt', 'w') as f:
        for dep in sorted(css_deps):
            f.write(f"{dep}\n")
    
    print("Audit complete! Files created:")
    print("- audit.tsv: File sizes")
    print("- js_deps.txt: JavaScript dependencies")
    print("- css_deps.txt: CSS dependencies")

if __name__ == "__main__":
    create_audit()
