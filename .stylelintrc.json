{"extends": ["stylelint-config-standard"], "rules": {"at-rule-no-unknown": [true, {"ignoreAtRules": ["import"]}], "selector-class-pattern": null, "selector-id-pattern": null, "custom-property-pattern": null, "keyframes-name-pattern": null, "function-name-case": null, "value-keyword-case": null, "property-no-vendor-prefix": null, "value-no-vendor-prefix": null, "selector-no-vendor-prefix": null, "media-feature-name-no-vendor-prefix": null, "at-rule-no-vendor-prefix": null, "declaration-block-no-redundant-longhand-properties": null, "shorthand-property-no-redundant-values": null, "comment-empty-line-before": null, "rule-empty-line-before": null, "at-rule-empty-line-before": null, "declaration-empty-line-before": null, "max-empty-lines": 2, "no-empty-source": null, "font-family-no-missing-generic-family-keyword": null}}