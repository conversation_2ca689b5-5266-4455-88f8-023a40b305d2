#!/usr/bin/env python3
"""
Comprehensive Post-Reorganization Fix Tool
Diagnoses and fixes all broken references after moving files to /src structure
"""

import os
import re
import json
import shutil
from pathlib import Path
from typing import List, Dict, Tuple
import argparse

class ReorganizationFixer:
    def __init__(self, root_dir: str = "."):
        self.root = Path(root_dir).resolve()
        self.src_dir = self.root / "src"
        self.issues = []
        self.fixes_applied = []
        
        # Path mapping for common moves
        self.path_mappings = {
            "assets/": "src/assets/",
            "data/": "src/data/",
            "alarm-sounds/": "src/assets/audio/alarms/",
            "sounds/": "src/assets/audio/sounds/",
            "relaxed-mode/": "src/pages/",
            "css/": "src/css/",
            "js/": "src/js/",
            "styles/": "src/css/",
        }
        
    def log_issue(self, severity: str, file_path: str, issue: str, fix: str = None):
        """Log an issue found during diagnosis"""
        self.issues.append({
            "severity": severity,
            "file": file_path,
            "issue": issue,
            "fix": fix,
            "fixed": False
        })
        
    def log_fix(self, file_path: str, description: str):
        """Log a fix that was applied"""
        self.fixes_applied.append({
            "file": file_path,
            "fix": description
        })

    def scan_html_files(self):
        """Scan HTML files for broken references"""
        print("🔍 Scanning HTML files...")
        
        html_files = list(self.root.rglob("*.html"))
        for html_file in html_files:
            try:
                content = html_file.read_text(encoding='utf-8')
                
                # Check for broken asset references
                patterns = [
                    (r'src=["\']([^"\']*)["\']', "src attribute"),
                    (r'href=["\']([^"\']*)["\']', "href attribute"),
                    (r'url\(["\']?([^"\']*)["\']?\)', "CSS url()"),
                ]
                
                for pattern, desc in patterns:
                    matches = re.findall(pattern, content, re.IGNORECASE)
                    for match in matches:
                        if self.is_broken_reference(match, html_file):
                            self.log_issue("HIGH", str(html_file), 
                                         f"Broken {desc}: {match}")
                            
            except Exception as e:
                self.log_issue("ERROR", str(html_file), f"Cannot read file: {e}")

    def scan_css_files(self):
        """Scan CSS files for broken url() references"""
        print("🔍 Scanning CSS files...")
        
        css_files = list(self.root.rglob("*.css"))
        for css_file in css_files:
            try:
                content = css_file.read_text(encoding='utf-8')
                
                # Find url() references
                url_pattern = r'url\(["\']?([^"\']*)["\']?\)'
                matches = re.findall(url_pattern, content, re.IGNORECASE)
                
                for match in matches:
                    if self.is_broken_reference(match, css_file):
                        self.log_issue("HIGH", str(css_file), 
                                     f"Broken CSS url(): {match}")
                        
            except Exception as e:
                self.log_issue("ERROR", str(css_file), f"Cannot read file: {e}")

    def scan_js_files(self):
        """Scan JavaScript files for broken imports and paths"""
        print("🔍 Scanning JavaScript files...")
        
        js_files = list(self.root.rglob("*.js"))
        for js_file in js_files:
            try:
                content = js_file.read_text(encoding='utf-8')
                
                # Check import statements
                import_patterns = [
                    (r'import\s+.*?from\s+["\']([^"\']*)["\']', "ES6 import"),
                    (r'require\(["\']([^"\']*)["\']', "CommonJS require"),
                    (r'fetch\(["\']([^"\']*)["\']', "fetch() call"),
                    (r'["\']([^"\']*\.(json|mp3|wav|png|jpg|jpeg|gif|svg))["\']', "Asset reference"),
                ]
                
                for pattern, desc in import_patterns:
                    matches = re.findall(pattern, content, re.IGNORECASE)
                    for match in matches:
                        if isinstance(match, tuple):
                            match = match[0]
                        if self.is_broken_reference(match, js_file):
                            self.log_issue("HIGH", str(js_file), 
                                         f"Broken {desc}: {match}")
                            
            except Exception as e:
                self.log_issue("ERROR", str(js_file), f"Cannot read file: {e}")

    def is_broken_reference(self, ref: str, from_file: Path) -> bool:
        """Check if a reference is broken"""
        # Skip external URLs, data URLs, and fragments
        if (ref.startswith(('http://', 'https://', 'data:', '#', '//', 'mailto:', 'tel:')) or
            not ref.strip()):
            return False
            
        # Resolve relative path
        if ref.startswith('/'):
            target = self.root / ref.lstrip('/')
        else:
            target = from_file.parent / ref
            
        # Remove query params and fragments
        target_str = str(target).split('?')[0].split('#')[0]
        target = Path(target_str)
        
        return not target.exists()

    def scan_config_files(self):
        """Scan configuration files for broken paths"""
        print("🔍 Scanning configuration files...")
        
        config_files = [
            "package.json", "firebase.json", ".firebaserc",
            "webpack.config.js", "vite.config.js", "rollup.config.js"
        ]
        
        for config_file in config_files:
            config_path = self.root / config_file
            if config_path.exists():
                try:
                    if config_file.endswith('.json'):
                        with open(config_path, 'r') as f:
                            config = json.load(f)
                            self.check_json_paths(config, str(config_path))
                    else:
                        content = config_path.read_text()
                        self.check_js_config_paths(content, str(config_path))
                        
                except Exception as e:
                    self.log_issue("ERROR", str(config_path), f"Cannot parse config: {e}")

    def check_json_paths(self, obj, file_path: str, path: str = ""):
        """Recursively check JSON object for file paths"""
        if isinstance(obj, dict):
            for key, value in obj.items():
                current_path = f"{path}.{key}" if path else key
                if isinstance(value, str) and self.looks_like_path(value):
                    if self.is_broken_reference(value, Path(file_path)):
                        self.log_issue("MEDIUM", file_path, 
                                     f"Broken path in {current_path}: {value}")
                else:
                    self.check_json_paths(value, file_path, current_path)
        elif isinstance(obj, list):
            for i, item in enumerate(obj):
                self.check_json_paths(item, file_path, f"{path}[{i}]")

    def looks_like_path(self, value: str) -> bool:
        """Check if a string looks like a file path"""
        return (isinstance(value, str) and 
                ('/' in value or '\\' in value) and
                not value.startswith(('http://', 'https://', 'data:')))

    def check_js_config_paths(self, content: str, file_path: str):
        """Check JavaScript config files for paths"""
        # Look for common path patterns in JS configs
        patterns = [
            r'entry:\s*["\']([^"\']*)["\']',
            r'output:\s*["\']([^"\']*)["\']',
            r'publicPath:\s*["\']([^"\']*)["\']',
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, content)
            for match in matches:
                if self.is_broken_reference(match, Path(file_path)):
                    self.log_issue("MEDIUM", file_path, f"Broken config path: {match}")

    def generate_fixes(self):
        """Generate automatic fixes for common issues"""
        print("🔧 Generating fixes...")
        
        for issue in self.issues:
            if issue["severity"] in ["HIGH", "MEDIUM"]:
                fix = self.suggest_fix(issue)
                if fix:
                    issue["fix"] = fix

    def suggest_fix(self, issue: Dict) -> str:
        """Suggest a fix for an issue"""
        issue_text = issue["issue"]
        
        # Extract the broken path
        for old_path, new_path in self.path_mappings.items():
            if old_path in issue_text:
                return f"Replace '{old_path}' with '{new_path}'"
                
        # Generic suggestions
        if "Broken src attribute" in issue_text:
            return "Update src path to new location in /src"
        elif "Broken CSS url()" in issue_text:
            return "Update CSS url() to new asset location"
        elif "Broken ES6 import" in issue_text:
            return "Update import path to new module location"
            
        return "Manual review required"

    def apply_fixes(self, auto_fix: bool = False):
        """Apply automatic fixes"""
        if not auto_fix:
            print("⚠️  Use --auto-fix to apply fixes automatically")
            return
            
        print("🔧 Applying fixes...")
        
        for issue in self.issues:
            if issue["fix"] and not issue["fixed"]:
                try:
                    self.apply_single_fix(issue)
                    issue["fixed"] = True
                    self.log_fix(issue["file"], issue["fix"])
                except Exception as e:
                    print(f"❌ Failed to fix {issue['file']}: {e}")

    def apply_single_fix(self, issue: Dict):
        """Apply a single fix"""
        file_path = Path(issue["file"])
        if not file_path.exists():
            return
            
        content = file_path.read_text(encoding='utf-8')
        
        # Apply path mappings
        for old_path, new_path in self.path_mappings.items():
            if old_path in issue["issue"]:
                # Replace the specific broken reference
                content = content.replace(old_path, new_path)
                
        file_path.write_text(content, encoding='utf-8')

    def run_diagnosis(self):
        """Run complete diagnosis"""
        print("🚀 Starting comprehensive diagnosis...")
        
        self.scan_html_files()
        self.scan_css_files()
        self.scan_js_files()
        self.scan_config_files()
        
        print(f"\n📊 Diagnosis complete: {len(self.issues)} issues found")
        
    def print_report(self):
        """Print detailed report"""
        if not self.issues:
            print("✅ No issues found!")
            return
            
        print("\n" + "="*60)
        print("🔍 REORGANIZATION ISSUES REPORT")
        print("="*60)
        
        severity_counts = {}
        for issue in self.issues:
            severity = issue["severity"]
            severity_counts[severity] = severity_counts.get(severity, 0) + 1
            
        print(f"\n📈 Summary:")
        for severity, count in severity_counts.items():
            emoji = {"HIGH": "🔴", "MEDIUM": "🟡", "LOW": "🟢", "ERROR": "💥"}
            print(f"  {emoji.get(severity, '⚪')} {severity}: {count}")
            
        print(f"\n📋 Detailed Issues:")
        for i, issue in enumerate(self.issues, 1):
            emoji = {"HIGH": "🔴", "MEDIUM": "🟡", "LOW": "🟢", "ERROR": "💥"}
            status = "✅ FIXED" if issue.get("fixed") else "❌ PENDING"
            
            print(f"\n{i}. {emoji.get(issue['severity'], '⚪')} {issue['severity']} - {status}")
            print(f"   File: {issue['file']}")
            print(f"   Issue: {issue['issue']}")
            if issue.get("fix"):
                print(f"   Fix: {issue['fix']}")

def main():
    parser = argparse.ArgumentParser(description="Fix reorganization issues")
    parser.add_argument("--auto-fix", action="store_true", 
                       help="Automatically apply fixes")
    parser.add_argument("--root", default=".", 
                       help="Root directory to scan")
    
    args = parser.parse_args()
    
    fixer = ReorganizationFixer(args.root)
    fixer.run_diagnosis()
    fixer.generate_fixes()
    fixer.print_report()
    
    if args.auto_fix:
        fixer.apply_fixes(auto_fix=True)
        print(f"\n✅ Applied {len(fixer.fixes_applied)} fixes")

if __name__ == "__main__":
    main()
