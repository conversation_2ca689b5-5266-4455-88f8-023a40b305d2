# Simulation Sound Effects

This folder contains sound effects for the multi-sensory simulation enhancer.

## Sound Files

- `click.mp3` - Sound effect for button clicks and interactions
- `hover.mp3` - Sound effect for hovering over interactive elements
- `transition.mp3` - Sound effect for transitioning between steps
- `success.mp3` - Sound effect for successful actions
- `error.mp3` - Sound effect for errors or warnings

## Usage

These sound files are used by the `simulation-enhancer.js` module to provide audio feedback in simulations.

If the sound files are not found, the enhancer will generate fallback sounds using the Web Audio API.
