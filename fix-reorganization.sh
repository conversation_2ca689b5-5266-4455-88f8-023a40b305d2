#!/bin/bash
# Comprehensive Post-Reorganization Fix Workflow
# Automated diagnosis and repair of broken references after file reorganization

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="${1:-$(pwd)}"
BACKUP_DIR="${PROJECT_ROOT}/.reorganization-backup"
LOG_FILE="${PROJECT_ROOT}/reorganization-fix.log"

# Logging function
log() {
    echo -e "${2:-$NC}[$(date '+%Y-%m-%d %H:%M:%S')] $1${NC}" | tee -a "$LOG_FILE"
}

# Error handling
error_exit() {
    log "ERROR: $1" "$RED"
    exit 1
}

# Success message
success() {
    log "SUCCESS: $1" "$GREEN"
}

# Warning message
warning() {
    log "WARNING: $1" "$YELLOW"
}

# Info message
info() {
    log "INFO: $1" "$BLUE"
}

# Create backup before making changes
create_backup() {
    info "Creating backup before applying fixes..."
    
    if [ -d "$BACKUP_DIR" ]; then
        warning "Backup directory already exists, removing old backup"
        rm -rf "$BACKUP_DIR"
    fi
    
    mkdir -p "$BACKUP_DIR"
    
    # Backup critical files
    find "$PROJECT_ROOT" -maxdepth 3 \( -name "*.html" -o -name "*.css" -o -name "*.js" -o -name "*.json" \) \
        -not -path "*/.git/*" \
        -not -path "*/node_modules/*" \
        -not -path "*/.reorganization-backup/*" \
        -exec cp --parents {} "$BACKUP_DIR" \; 2>/dev/null || true
    
    success "Backup created at $BACKUP_DIR"
}

# Restore from backup
restore_backup() {
    if [ ! -d "$BACKUP_DIR" ]; then
        error_exit "No backup found to restore from"
    fi
    
    warning "Restoring from backup..."
    cp -r "$BACKUP_DIR"/* "$PROJECT_ROOT"/ 2>/dev/null || true
    success "Backup restored"
}

# Check prerequisites
check_prerequisites() {
    info "Checking prerequisites..."
    
    # Check if Python 3 is available
    if ! command -v python3 &> /dev/null; then
        error_exit "Python 3 is required but not installed"
    fi
    
    # Check if we're in a project directory
    if [ ! -f "$PROJECT_ROOT/package.json" ] && [ ! -d "$PROJECT_ROOT/src" ]; then
        warning "This doesn't look like a web project directory"
    fi
    
    success "Prerequisites check passed"
}

# Run quick smoke test
smoke_test() {
    info "Running smoke test..."
    
    # Check if critical files exist
    local critical_files=("index.html" "src/js/app.js" "src/css")
    local missing_files=()
    
    for file in "${critical_files[@]}"; do
        if [ ! -e "$PROJECT_ROOT/$file" ]; then
            missing_files+=("$file")
        fi
    done
    
    if [ ${#missing_files[@]} -gt 0 ]; then
        warning "Missing critical files: ${missing_files[*]}"
    else
        success "Smoke test passed - critical files found"
    fi
}

# Run the Python diagnosis tool
run_diagnosis() {
    info "Running comprehensive diagnosis..."
    
    cd "$PROJECT_ROOT"
    python3 "$SCRIPT_DIR/fix-reorganization.py" --root . > diagnosis-report.txt 2>&1
    
    if [ $? -eq 0 ]; then
        success "Diagnosis completed - check diagnosis-report.txt"
    else
        error_exit "Diagnosis failed - check diagnosis-report.txt for details"
    fi
}

# Apply automatic fixes
apply_fixes() {
    info "Applying automatic fixes..."
    
    cd "$PROJECT_ROOT"
    python3 "$SCRIPT_DIR/fix-reorganization.py" --root . --auto-fix
    
    if [ $? -eq 0 ]; then
        success "Automatic fixes applied"
    else
        error_exit "Failed to apply fixes"
    fi
}

# Fix common path issues with sed
fix_common_paths() {
    info "Applying common path fixes..."
    
    # Fix HTML files
    find "$PROJECT_ROOT" -name "*.html" -not -path "*/.git/*" -not -path "*/node_modules/*" | while read -r file; do
        # Fix asset references
        sed -i.bak 's|src="assets/|src="src/assets/|g' "$file" 2>/dev/null || true
        sed -i.bak 's|href="assets/|href="src/assets/|g' "$file" 2>/dev/null || true
        sed -i.bak 's|src="css/|src="src/css/|g' "$file" 2>/dev/null || true
        sed -i.bak 's|href="css/|href="src/css/|g' "$file" 2>/dev/null || true
        sed -i.bak 's|src="js/|src="src/js/|g' "$file" 2>/dev/null || true
        
        # Remove backup files
        rm -f "${file}.bak" 2>/dev/null || true
    done
    
    # Fix CSS files
    find "$PROJECT_ROOT" -name "*.css" -not -path "*/.git/*" -not -path "*/node_modules/*" | while read -r file; do
        # Fix url() references
        sed -i.bak 's|url("../assets/|url("../assets/|g' "$file" 2>/dev/null || true
        sed -i.bak 's|url(../assets/|url(../assets/|g' "$file" 2>/dev/null || true
        
        # Remove backup files
        rm -f "${file}.bak" 2>/dev/null || true
    done
    
    # Fix JavaScript files
    find "$PROJECT_ROOT" -name "*.js" -not -path "*/.git/*" -not -path "*/node_modules/*" | while read -r file; do
        # Fix import paths
        sed -i.bak 's|from "assets/|from "src/assets/|g' "$file" 2>/dev/null || true
        sed -i.bak 's|from '\''assets/|from '\''src/assets/|g' "$file" 2>/dev/null || true
        sed -i.bak 's|"data/|"src/data/|g' "$file" 2>/dev/null || true
        sed -i.bak 's|'\''data/|'\''src/data/|g' "$file" 2>/dev/null || true
        
        # Remove backup files
        rm -f "${file}.bak" 2>/dev/null || true
    done
    
    success "Common path fixes applied"
}

# Update package.json scripts if needed
fix_package_json() {
    local package_json="$PROJECT_ROOT/package.json"
    
    if [ -f "$package_json" ]; then
        info "Checking package.json for path updates..."
        
        # Create backup
        cp "$package_json" "${package_json}.bak"
        
        # Fix common script paths (this is basic - the Python tool handles complex cases)
        sed -i 's|"css/|"src/css/|g' "$package_json" 2>/dev/null || true
        sed -i 's|"js/|"src/js/|g' "$package_json" 2>/dev/null || true
        
        success "package.json updated"
    fi
}

# Validate fixes
validate_fixes() {
    info "Validating applied fixes..."
    
    # Run diagnosis again to see remaining issues
    cd "$PROJECT_ROOT"
    python3 "$SCRIPT_DIR/fix-reorganization.py" --root . > validation-report.txt 2>&1
    
    # Count remaining issues
    local remaining_issues=$(grep -c "🔴\|🟡" validation-report.txt 2>/dev/null || echo "0")
    
    if [ "$remaining_issues" -eq 0 ]; then
        success "All issues resolved!"
    else
        warning "$remaining_issues issues remain - check validation-report.txt"
    fi
}

# Clean up temporary files
cleanup() {
    info "Cleaning up temporary files..."
    
    # Remove backup files created by sed
    find "$PROJECT_ROOT" -name "*.bak" -not -path "*/.git/*" -delete 2>/dev/null || true
    
    success "Cleanup completed"
}

# Main workflow
main() {
    echo -e "${BLUE}"
    echo "=================================================="
    echo "🔧 POST-REORGANIZATION FIX WORKFLOW"
    echo "=================================================="
    echo -e "${NC}"
    
    info "Starting fix workflow for: $PROJECT_ROOT"
    
    # Initialize log
    echo "Reorganization Fix Log - $(date)" > "$LOG_FILE"
    
    # Run workflow steps
    check_prerequisites
    smoke_test
    create_backup
    
    # Offer choice of fix methods
    echo -e "\n${YELLOW}Choose fix method:${NC}"
    echo "1) Quick fix (sed-based path replacements)"
    echo "2) Comprehensive fix (Python-based analysis + auto-fix)"
    echo "3) Both (recommended)"
    echo "4) Diagnosis only"
    
    read -p "Enter choice (1-4): " choice
    
    case $choice in
        1)
            fix_common_paths
            fix_package_json
            ;;
        2)
            run_diagnosis
            apply_fixes
            ;;
        3)
            fix_common_paths
            fix_package_json
            run_diagnosis
            apply_fixes
            ;;
        4)
            run_diagnosis
            ;;
        *)
            error_exit "Invalid choice"
            ;;
    esac
    
    validate_fixes
    cleanup
    
    echo -e "\n${GREEN}"
    echo "=================================================="
    echo "✅ FIX WORKFLOW COMPLETED"
    echo "=================================================="
    echo -e "${NC}"
    
    info "Check the following files for details:"
    info "- $LOG_FILE (complete log)"
    info "- diagnosis-report.txt (initial issues)"
    info "- validation-report.txt (remaining issues)"
    
    if [ -d "$BACKUP_DIR" ]; then
        info "Backup available at: $BACKUP_DIR"
        echo -e "\n${YELLOW}To restore backup if needed: $0 restore${NC}"
    fi
}

# Handle restore command
if [ "${1:-}" = "restore" ]; then
    restore_backup
    exit 0
fi

# Run main workflow
main "$@"
