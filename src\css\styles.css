/**
 * GPAce Unified Styles
 * Single CSS file containing all application styles
 */

/* External CSS Imports */
@import url('https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css');
@import url('https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css');
@import url('https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css');
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap');

/* Base Styles */
@import url('./main.css');
@import url('./sideDrawer.css');
@import url('./notification.css');

/* Component Styles */
@import url('./task-display.css');
@import url('./text-expansion.css');
@import url('./taskLinks.css');

/* Page-Specific Styles */
@import url('./grind.css');
@import url('./flashcards.css');
@import url('./academic-details.css');
@import url('./workspace.css');
@import url('./study-spaces.css');
@import url('./daily-calendar.css');
@import url('./extracted.css');
@import url('./subject-marks.css');
@import url('./settings.css');
@import url('./priority-calculator.css');
@import url('./priority-list.css');
@import url('./sleep-saboteurs.css');
@import url('./test-feedback.css');
@import url('./ai-search-response.css');
@import url('./task-notes.css');
@import url('./alarm-service.css');
@import url('./simulation-enhancer.css');
@import url('./compact-style.css');

/* Loading Screen Styles */
.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #121212, #1e1e1e);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    transition: opacity 0.5s ease;
}

.loading-screen.hidden {
    opacity: 0;
    pointer-events: none;
}

.loading-logo {
    width: 80px;
    height: 80px;
    margin-bottom: 20px;
    animation: pulse 2s infinite;
}

.loading-text {
    color: #ffffff;
    font-size: 1.2rem;
    margin-bottom: 10px;
}

.loading-subtext {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.9rem;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid rgba(254, 44, 85, 0.3);
    border-top: 3px solid #fe2c55;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-top: 20px;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* App Container Styles */
#root {
    min-height: 100vh;
    background-color: #121212;
    color: #ffffff;
}

.page-container {
    opacity: 0;
    transition: opacity 0.3s ease;
}

.page-container.loaded {
    opacity: 1;
}

/* Error Page Styles */
.error-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 100vh;
    text-align: center;
    padding: 20px;
}

.error-code {
    font-size: 6rem;
    font-weight: bold;
    background: linear-gradient(45deg, #fe2c55, #25f4ee);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    margin-bottom: 20px;
}

.error-message {
    font-size: 1.5rem;
    margin-bottom: 30px;
    color: rgba(255, 255, 255, 0.8);
}

.error-actions {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
    justify-content: center;
}

.btn-primary {
    background: linear-gradient(135deg, #fe2c55, #25f4ee);
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    color: white;
    text-decoration: none;
    transition: transform 0.3s ease;
}

.btn-primary:hover {
    transform: translateY(-2px);
    color: white;
}

/* Router Transition Styles */
.page-transition {
    transition: opacity 0.3s ease, transform 0.3s ease;
}

.page-transition.entering {
    opacity: 0;
    transform: translateY(20px);
}

.page-transition.entered {
    opacity: 1;
    transform: translateY(0);
}

.page-transition.exiting {
    opacity: 0;
    transform: translateY(-20px);
}

/* Navigation Styles */
.top-nav {
    background: rgba(26, 26, 26, 0.9);
    backdrop-filter: blur(10px);
    padding: 15px 0;
    position: sticky;
    top: 0;
    z-index: 1000;
    transition: all 0.3s ease;
}

.nav-brand {
    display: flex;
    align-items: center;
    color: #ffffff;
    text-decoration: none;
    font-size: 1.5rem;
    font-weight: 600;
}

.nav-brand img {
    height: 60px;
    margin-right: 10px;
}

.nav-links {
    display: flex;
    align-items: center;
    gap: 20px;
    flex-wrap: wrap;
}

.nav-links a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    padding: 8px 16px;
    border-radius: 6px;
    transition: all 0.3s ease;
    font-weight: 500;
}

.nav-links a:hover,
.nav-links a.active {
    color: #ffffff;
    background: linear-gradient(135deg, rgba(254, 44, 85, 0.2), rgba(37, 244, 238, 0.2));
    transform: translateY(-2px);
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-links {
        flex-direction: column;
        gap: 10px;
    }
    
    .nav-links a {
        padding: 12px 20px;
        width: 100%;
        text-align: center;
    }
    
    .error-code {
        font-size: 4rem;
    }
    
    .error-actions {
        flex-direction: column;
        align-items: center;
    }
}

/* Utility Classes */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

.fade-out {
    animation: fadeOut 0.5s ease-out;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes fadeOut {
    from { opacity: 1; }
    to { opacity: 0; }
}

.slide-up {
    animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
    from { 
        opacity: 0;
        transform: translateY(30px);
    }
    to { 
        opacity: 1;
        transform: translateY(0);
    }
}

/* Print Styles */
@media print {
    .loading-screen,
    .top-nav,
    .drawer-toggle,
    .side-drawer {
        display: none !important;
    }
    
    #root {
        background: white !important;
        color: black !important;
    }
}
