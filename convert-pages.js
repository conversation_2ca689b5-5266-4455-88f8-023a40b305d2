#!/usr/bin/env node

/**
 * Page Converter Script
 * Converts existing HTML pages to work with the new router system
 * Removes duplicate head content and updates links
 */

const fs = require('fs');
const path = require('path');

const PAGES_DIR = './src/pages';
const OUTPUT_DIR = './src/pages';

// Pages to convert
const pagesToConvert = [
    'landing.html',
    'grind.html', 
    'tasks.html',
    'workspace.html',
    'flashcards.html',
    'academic-details.html',
    'study-spaces.html',
    'daily-calendar.html',
    'instant-test-feedback.html',
    'extracted.html',
    'subject-marks.html',
    'settings.html',
    'priority-calculator.html',
    'priority-list.html',
    'sleep-saboteurs.html'
];

function convertPage(filename) {
    const filePath = path.join(PAGES_DIR, filename);
    
    if (!fs.existsSync(filePath)) {
        console.log(`⚠️  File not found: ${filename}`);
        return;
    }
    
    console.log(`🔄 Converting: ${filename}`);
    
    let content = fs.readFileSync(filePath, 'utf8');
    
    // Extract body content only
    const bodyMatch = content.match(/<body[^>]*>([\s\S]*?)<\/body>/i);
    if (!bodyMatch) {
        console.log(`⚠️  No body content found in: ${filename}`);
        return;
    }
    
    let bodyContent = bodyMatch[1];
    
    // Update internal links to use router navigation
    bodyContent = bodyContent.replace(/href="([^"]+\.html)"/g, (match, url) => {
        // Convert .html links to router paths
        const routePath = url.replace('.html', '').replace(/^\.\//, '/');
        return `href="${routePath}" onclick="event.preventDefault(); window.navigate('${routePath}')"`;
    });
    
    // Update asset paths to be relative to root
    bodyContent = bodyContent.replace(/src="(?!http|\/\/|data:)([^"]+)"/g, (match, src) => {
        if (src.startsWith('src/')) return match; // Already correct
        if (src.startsWith('/')) return match; // Absolute path
        
        // Convert relative paths
        let newSrc = src;
        if (src.startsWith('assets/')) {
            newSrc = `src/${src}`;
        } else if (src.startsWith('js/')) {
            newSrc = `src/${src}`;
        } else if (src.startsWith('css/')) {
            newSrc = `src/${src}`;
        } else if (src.startsWith('sounds/') || src.startsWith('alarm-sounds/')) {
            newSrc = `src/assets/audio/${src.split('/').pop()}`;
        }
        
        return `src="${newSrc}"`;
    });
    
    // Update CSS href paths
    bodyContent = bodyContent.replace(/href="(?!http|\/\/|data:)([^"]+\.css)"/g, (match, href) => {
        if (href.startsWith('src/')) return match; // Already correct
        if (href.startsWith('/')) return match; // Absolute path
        
        let newHref = href;
        if (href.startsWith('css/')) {
            newHref = `src/${href}`;
        } else if (href.startsWith('styles/')) {
            newHref = `src/css/${href.replace('styles/', '')}`;
        }
        
        return `href="${newHref}"`;
    });
    
    // Remove script tags that are now handled by the main app
    bodyContent = bodyContent.replace(/<script[^>]*src="[^"]*(?:common|firebase|auth|cross-tab-sync)[^"]*"[^>]*><\/script>/gi, '');
    
    // Remove duplicate CSS links that are now in main index.html
    bodyContent = bodyContent.replace(/<link[^>]*href="[^"]*(?:bootstrap|font-awesome|bootstrap-icons)[^"]*"[^>]*>/gi, '');
    
    // Add page identifier comment
    const pageName = filename.replace('.html', '');
    const convertedContent = `<!-- ${pageName} page content -->\n${bodyContent}`;
    
    // Write converted content
    const outputPath = path.join(OUTPUT_DIR, filename);
    fs.writeFileSync(outputPath, convertedContent, 'utf8');
    
    console.log(`✅ Converted: ${filename}`);
}

function convertAllPages() {
    console.log('🚀 Starting page conversion...\n');
    
    // Ensure output directory exists
    if (!fs.existsSync(OUTPUT_DIR)) {
        fs.mkdirSync(OUTPUT_DIR, { recursive: true });
    }
    
    // Convert each page
    pagesToConvert.forEach(convertPage);
    
    console.log('\n✅ Page conversion complete!');
    console.log('\n📋 Next steps:');
    console.log('1. Test the new router by opening index.html');
    console.log('2. Update any remaining script references');
    console.log('3. Test navigation between pages');
    console.log('4. Update any page-specific JavaScript modules');
}

// Run conversion if called directly
if (require.main === module) {
    convertAllPages();
}

module.exports = { convertPage, convertAllPages };
