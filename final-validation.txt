Traceback (most recent call last):
  File "E:\Improving GPAce\Creating an App\fix-reorganization.py", line 329, in <module>
    main()
    ~~~~^^
  File "E:\Improving GPAce\Creating an App\fix-reorganization.py", line 320, in main
    fixer.run_diagnosis()
    ~~~~~~~~~~~~~~~~~~~^^
  File "E:\Improving GPAce\Creating an App\fix-reorganization.py", line 270, in run_diagnosis
    print("\U0001f680 Starting comprehensive diagnosis...")
    ~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>
