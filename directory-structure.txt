Folder PATH listing for volume Games n Stuff
Volume serial number is 1806-1780
E:.
|   .env
|   .firebaseignore
|   .firebaserc
|   .gitignore
|   404.html
|   academic-details.html
|   CONTRIBUTOR_LICENSE_AGREEMENT.md
|   cross-tab-sync-standardization.md
|   daily-calendar.html
|   directory-structure.txt
|   end.txt
|   extracted.html
|   filelist.txt
|   firebase.json
|   flashcards.html
|   grind.css
|   grind.html
|   index.html
|   instant-test-feedback.html
|   landing.html
|   package-lock.json
|   package.json
|   pop.mp3
|   priority-calculator-with-worker.js
|   priority-calculator.html
|   priority-calculator.js
|   priority-list.html
|   priority-worker-README.md
|   README.md
|   server.js
|   settings.html
|   sideDrawer-standardization-documentation.md
|   sleep-saboteurs.html
|   start-server.bat
|   study-spaces.html
|   subject-marks.html
|   task-links-fix-documentation.md
|   task-notes-improvements-2023-07-10-1530.md
|   tasks.html
|   test-worker.js
|   worker.js
|   workspace.html
|   
+---alarm-sounds
|       alarm1.mp3
|       alarm2.mp3
|       alarm3.mp3
|       README.md
|       
+---assets
|   +---audio
|   \---images
|           gpace-logo-white.png
|           
+---css
|       academic-details.css
|       ai-search-response.css
|       alarm-service.css
|       compact-style.css
|       daily-calendar.css
|       extracted.css
|       flashcards.css
|       notification.css
|       priority-calculator.css
|       priority-list.css
|       settings.css
|       sideDrawer.css
|       simulation-enhancer.css
|       sleep-saboteurs.css
|       study-spaces.css
|       subject-marks.css
|       task-display.css
|       task-notes.css
|       taskLinks.css
|       test-feedback.css
|       text-expansion.css
|       workspace.css
|       
+---data
|   |   locations.json
|   |   schedule.json
|   |   timetable.json
|   |   
|   \---energy-logs
|           2024-11-26.json
|           
+---icons
+---js
|   |   academic-details.js
|   |   add-favicon.js
|   |   ai-latex-conversion.js
|   |   ai-researcher.js
|   |   alarm-data-service.js
|   |   alarm-handler.js
|   |   alarm-mini-display.js
|   |   alarm-service-worker.js
|   |   alarm-service.js
|   |   api-optimization.js
|   |   api-settings.js
|   |   apiSettingsManager.js
|   |   auth.js
|   |   calendar-views.js
|   |   calendarManager.js
|   |   clock-display.js
|   |   common-header.js
|   |   common.js
|   |   cross-tab-sync.js
|   |   currentTaskManager.js
|   |   data-loader.js
|   |   data-sync-integration.js
|   |   data-sync-manager.js
|   |   energyHologram.js
|   |   energyLevels.js
|   |   fileViewer.js
|   |   firebase-config.js
|   |   firebase-init.js
|   |   firebaseAuth.js
|   |   firebaseConfig.js
|   |   firestore-global.js
|   |   firestore.js
|   |   flashcardManager.js
|   |   flashcards.js
|   |   flashcardTaskIntegration.js
|   |   gemini-api.js
|   |   googleDriveApi.js
|   |   googleGenerativeAI.js
|   |   grind-speech-synthesis.js
|   |   imageAnalyzer.js
|   |   indexedDB.js
|   |   initFirestoreData.js
|   |   inject-header.js
|   |   markdown-converter.js
|   |   marks-tracking.js
|   |   pandoc-fallback.js
|   |   pomodoroGlobal.js
|   |   pomodoroTimer.js
|   |   priority-list-sorting.js
|   |   priority-list-utils.js
|   |   priority-sync-fix.js
|   |   priority-worker-wrapper.js
|   |   quoteManager.js
|   |   recipeManager.js
|   |   reorganize-scripts.js
|   |   roleModelManager.js
|   |   scheduleManager.js
|   |   semester-management.js
|   |   sideDrawer.js
|   |   simulation-enhancer.js
|   |   sleep-saboteurs-init.js
|   |   sleepScheduleManager.js
|   |   sleepTimeCalculator.js
|   |   sm2.js
|   |   soundManager.js
|   |   speech-recognition.js
|   |   speech-synthesis.js
|   |   storageManager.js
|   |   studySpaceAnalyzer.js
|   |   studySpacesFirestore.js
|   |   studySpacesManager.js
|   |   subject-management.js
|   |   subject-marks-integration.js
|   |   subject-marks-ui.js
|   |   subject-marks.js
|   |   task-notes-injector.js
|   |   task-notes.js
|   |   taskAttachments.js
|   |   taskFilters.js
|   |   taskLinks.js
|   |   tasksManager.js
|   |   test-feedback.js
|   |   text-expansion.js
|   |   theme-manager.js
|   |   themeManager.js
|   |   timetableAnalyzer.js
|   |   timetableIntegration.js
|   |   todoistIntegration.js
|   |   transitionManager.js
|   |   ui-utilities.js
|   |   update-html-files.js
|   |   userGuidance.js
|   |   weightage-connector.js
|   |   workspace-attachments.js
|   |   workspace-core.js
|   |   workspace-document.js
|   |   workspace-formatting.js
|   |   workspace-media.js
|   |   workspace-tables-links.js
|   |   workspace-ui.js
|   |   workspaceFlashcardIntegration.js
|   |   
|   \---simulation
+---public
|   |   service-worker.js
|   |   
|   \---js
|           cacheManager.js
|           
+---relaxed-mode
|       index.html
|       script.js
|       style.css
|       
+---scripts
|       theme.js
|       
+---server
|   |   dataStorage.js
|   |   timetableHandler.js
|   |   
|   \---routes
|           subtasks.js
|           
+---sounds
|       notification.mp3
|       pop.mp3
|       README.md
|       
+---styles
|       calendar.css
|       index.css
|       main.css
|       study-spaces.css
|       tasks.css
|       
+---temp
+---uploads
|   |   .gitkeep
|   |   README.md
|   |   
|   \---default
|           image-1732579461232-788949872.avif
|           image-1732589543717-403935461.png
|           image-1732590318655-964870304.PNG
|           image-1732592000086-814224384.PNG
|           image-1732592122316-699001170.PNG
|           image-1732592236266-95194259.PNG
|           image-1732592390471-529581569.PNG
|           image-1732592690026-536542048.PNG
|           image-1732592709631-334330466.PNG
|           image-1732592713170-576016821.png
|           image-1732592716169-992113737.jpg
|           image-1732592822832-979099085.PNG
|           image-1732592841381-484398440.PNG
|           image-1732592845636-848162219.PNG
|           image-1732592848492-144262374.PNG
|           image-1732592851327-112531886.PNG
|           image-1732600452839-328172200.PNG
|           image-1732600659543-206523209.PNG
|           image-1732600943333-461470707.PNG
|           image-1732601153906-856259396.PNG
|           image-1732601469488-219596759.PNG
|           image-1732602371601-29804403.PNG
|           image-1732602403329-408985608.PNG
|           image-1732602536212-815681537.PNG
|           image-1732602566710-343512043.PNG
|           image-1732602596940-409089044.PNG
|           image-1732602644097-663961924.PNG
|           image-1732605996470-848400140.PNG
|           image-1732615201141-371091482.PNG
|           image-1732619511845-772776538.png
|           image-1732620210230-15997120.PNG
|           image-1732620262639-895276401.PNG
|           image-1732620343618-475356922.PNG
|           image-1732620648456-721262359.PNG
|           image-1732679739599-85462180.PNG
|           image-1732679783475-369713894.PNG
|           image-1732689040322-560250155.PNG
|           image-1732689925308-644825392.PNG
|           image-1732690227330-678336383.PNG
|           image-1732698761896-360712059.jpg
|           image-1732699620986-777739954.PNG
|           image-1732700573679-743070572.PNG
|           image-1732706904364-403772819.PNG
|           image-1732749554068-590947980.PNG
|           image-1732749717062-684653347.PNG
|           image-1732749733259-843563255.PNG
|           image-1732751420496-708168326.PNG
|           image-1732760197916-325464671.PNG
|           image-1732760842304-7618147.PNG
|           image-1732797748740-823191504.PNG
|           image-1732797766614-39561390.PNG
|           image-1732797881091-623832537.PNG
|           image-1732797901005-193843066.PNG
|           image-1732837960997-325938940.PNG
|           image-1732838553405-496053154.PNG
|           image-1732839953048-86763601.PNG
|           image-1732840042878-129967629.PNG
|           image-1732841431470-897825949.PNG
|           image-1732843068323-653708464.PNG
|           image-1732843151248-36036156.PNG
|           image-1732846508575-213395532.PNG
|           image-1732846545160-493645044.PNG
|           image-1732847514388-450248478.PNG
|           image-1732847561888-384056758.PNG
|           image-1732847698162-801466827.PNG
|           image-1732847856124-451974442.PNG
|           image-1732847995061-326842834.PNG
|           image-1732848046609-525049164.PNG
|           image-1732848227184-397887927.PNG
|           image-1732848425929-857821658.PNG
|           image-1732848537899-232958068.PNG
|           image-1732848719720-737429281.PNG
|           image-1732850230721-823899339.PNG
|           image-1732850554425-365961927.PNG
|           image-1732851319904-740437277.PNG
|           image-1732851332694-720547491.PNG
|           image-1732851589490-808146140.PNG
|           image-1732852700047-174980699.PNG
|           image-1732852872553-820630918.PNG
|           image-1732856057169-180628067.PNG
|           image-1732856084276-128033965.PNG
|           image-1732856239191-131086653.PNG
|           image-1732856350272-569880006.PNG
|           image-1732856510359-488228846.PNG
|           image-1732856693588-28177288.PNG
|           image-1732858398167-373305146.jpg
|           image-1732859225898-98663998.jpg
|           image-1732872924297-455348616.PNG
|           image-1732872971159-28342234.PNG
|           image-1732873460376-387747542.jpg
|           image-1732886025060-297000140.PNG
|           image-1732886287248-668418863.jpg
|           image-1732921809482-981276870.jpg
|           image-1732947394515-681807177.png
|           image-1733024025576-923578314.PNG
|           image-1733632056328-53380198.jpg
|           image-1733632584090-866665893.PNG
|           image-1733635216268-171274801.jpg
|           image-1733638313215-278793451.PNG
|           image-1733638637643-427775793.jpg
|           image-1733638657775-556379286.jpg
|           image-1733638686832-808315327.PNG
|           image-1733638728192-956614887.jpg
|           image-1733638773382-800315798.jpg
|           image-1733638905122-693608300.PNG
|           image-1733638936661-230697789.PNG
|           image-1733639025893-255896971.PNG
|           image-1733639055975-229472824.PNG
|           image-1733639149861-302192940.PNG
|           image-1733639251182-862762631.jpg
|           image-1733639448929-598043345.PNG
|           image-1733639515823-833677430.PNG
|           image-1733639537843-617657171.PNG
|           image-1733663141160-486089733.PNG
|           image-1733663760573-606419978.jpg
|           image-1733665172571-853104420.PNG
|           image-1733741213436-527152276.PNG
|           image-1733741238342-138371976.jpg
|           image-1734410453018-55350789.PNG
|           image-1734586985698-507959043.png
|           image-1734747598140-10679473.png
|           image-1741102659089-932219673.png
|           image-1741103075402-31512396.png
|           image-1742018564314-921624356.jpg
|           image-1742018805123-901377952.jpg
|           image-1742018916533-877400396.jpg
|           image-1742019220611-424754200.jpg
|           image-1742019415300-59497668.jpg
|           image-1742019514886-621109285.jpg
|           image-1742020515985-220752860.jpg
|           image-1742020561546-800526907.jpg
|           image-1742020646063-814278381.jpg
|           image-1742020711321-855768177.jpg
|           image-1742020891099-324270051.jpg
|           image-1742022042836-455267428.jpg
|           image-1743980207473-497755135.jpg
|           settings.json
|           
\---workers
        imageAnalysis.js
        
