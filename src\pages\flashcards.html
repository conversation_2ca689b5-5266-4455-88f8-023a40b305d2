

    <!-- Navigation Bar -->
    <nav class="top-nav">
        <div class="nav-brand d-flex align-items-center">
            <img src="assets/images/gpace-logo-white.png" alt="GPAce Logo" style="height: 60px; margin-right: 0px;">
            <a href="grind.html" style="text-decoration: none; color: inherit;">GPAce</a>
        </div>
        <div class="nav-links">
            <a href="grind.html">Grind Mode</a>
            <a href="study-spaces.html">Grind Station</a>
            <a href="daily-calendar.html">Daily Drip</a>
            <a href="academic-details.html">Brain Juice</a>
            <a href="extracted.html">Hustle Hub</a>
            <a href="subject-marks.html">Subject Marks</a>
            <a href="flashcards.html" class="active">Flashcards</a>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row">
            <div class="col-md-8 offset-md-2">
                <!-- Subject Selection -->
                <div class="card mb-4">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">Flashcard Decks</h5>
                        <button class="btn btn-primary" id="createDeckBtn">
                            <i class="bi bi-plus-lg"></i> Create Deck
                        </button>
                    </div>
                    <div class="card-body">
                        <div class="row" id="decksList">
                            <!-- Decks will be loaded here dynamically -->
                            <div class="col-12 text-center py-5" id="noDecksMessage">
                                <i class="bi bi-card-list fs-1 text-muted"></i>
                                <p class="mt-3 text-muted">No flashcard decks found. Create your first deck to get started!</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Flashcard Study Modal -->
    <div class="modal fade" id="studyModal" tabindex="-1" aria-labelledby="studyModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="studyModalLabel">Study Flashcards</h5>
                    <div class="ms-auto me-2">
                        <span class="badge bg-primary" id="progressCounter">0/0</span>
                    </div>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="flashcard-container">
                        <div class="flashcard" id="currentFlashcard">
                            <div class="flashcard-inner">
                                <div class="flashcard-front">
                                    <div class="flashcard-content" id="questionContent">
                                        <!-- Question will be displayed here -->
                                    </div>
                                </div>
                                <div class="flashcard-back">
                                    <div class="flashcard-content" id="answerContent">
                                        <!-- Answer will be displayed here -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="text-center mt-4" id="flipCardPrompt">
                        <button class="btn btn-outline-primary" id="flipCardBtn">
                            <i class="bi bi-arrow-repeat"></i> Flip Card
                        </button>
                    </div>

                    <div class="rating-container mt-4" id="ratingContainer" style="display: none;">
                        <p class="text-center mb-2">How well did you know this?</p>
                        <div class="d-flex justify-content-center">
                            <button class="btn btn-outline-danger mx-1 rating-btn" data-rating="1">
                                <i class="bi bi-emoji-frown"></i> Not at all
                            </button>
                            <button class="btn btn-outline-warning mx-1 rating-btn" data-rating="2">
                                <i class="bi bi-emoji-neutral"></i> Barely
                            </button>
                            <button class="btn btn-outline-info mx-1 rating-btn" data-rating="3">
                                <i class="bi bi-emoji-smile"></i> Somewhat
                            </button>
                            <button class="btn btn-outline-success mx-1 rating-btn" data-rating="4">
                                <i class="bi bi-emoji-laughing"></i> Well
                            </button>
                            <button class="btn btn-outline-primary mx-1 rating-btn" data-rating="5">
                                <i class="bi bi-emoji-sunglasses"></i> Perfectly
                            </button>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Create Deck Modal -->
    <div class="modal fade" id="createDeckModal" tabindex="-1" aria-labelledby="createDeckModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="createDeckModalLabel">Create New Deck</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="createDeckForm">
                        <div class="mb-3">
                            <label for="deckTitle" class="form-label">Deck Title</label>
                            <input type="text" class="form-control" id="deckTitle" required>
                        </div>
                        <div class="mb-3">
                            <label for="deckSubject" class="form-label">Subject</label>
                            <select class="form-select" id="deckSubject" required>
                                <option value="">Select a subject</option>
                                <!-- Subjects will be loaded dynamically -->
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="deckDescription" class="form-label">Description (Optional)</label>
                            <textarea class="form-control" id="deckDescription" rows="3"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" id="saveDeckBtn">Create Deck</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Card Modal -->
    <div class="modal fade" id="addCardModal" tabindex="-1" aria-labelledby="addCardModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addCardModalLabel">Add New Flashcard</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="addCardForm">
                        <input type="hidden" id="currentDeckId">
                        <div class="mb-3">
                            <label for="cardQuestion" class="form-label">Question</label>
                            <textarea class="form-control" id="cardQuestion" rows="3" required></textarea>
                        </div>
                        <div class="mb-3">
                            <label for="cardAnswer" class="form-label">Answer</label>
                            <textarea class="form-control" id="cardAnswer" rows="3" required></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" id="saveCardBtn">Add Card</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Deck Details Modal -->
    <div class="modal fade" id="deckDetailsModal" tabindex="-1" aria-labelledby="deckDetailsModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="deckDetailsModalLabel">Deck Details</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="deck-info mb-4">
                        <h3 id="deckDetailTitle">Deck Title</h3>
                        <p id="deckDetailSubject" class="text-muted">Subject</p>
                        <p id="deckDetailDescription">Description</p>
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <span class="badge bg-primary me-2" id="cardCount">0 cards</span>
                                <span class="badge bg-info" id="dueCount">0 due</span>
                            </div>
                            <button class="btn btn-primary" id="addCardBtn">
                                <i class="bi bi-plus-lg"></i> Add Card
                            </button>
                        </div>
                    </div>

                    <div class="table-responsive">
                        <table class="table table-hover" id="cardsTable">
                            <thead>
                                <tr>
                                    <th>Question</th>
                                    <th>Answer</th>
                                    <th>Due Date</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody id="cardsTableBody">
                                <!-- Cards will be loaded here dynamically -->
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-danger me-auto" id="deleteDeckBtn">Delete Deck</button>
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="button" class="btn btn-success" id="studyDeckBtn">Study Now</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script type="importmap">
    {
        "imports": {
            "firebase/app": "https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js",
            "firebase/firestore": "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js",
            "firebase/auth": "https://www.gstatic.com/firebasejs/10.7.1/firebase-auth.js"
        }
    }
    </script>
    
    

    <!-- Firebase Initialization -->
    <script type="module">
        import { initializeApp } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js';
        import { getFirestore } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js';
        import { getAuth } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-auth.js';
        import { firebaseConfig } from './js/firebaseConfig.js';

        // Initialize Firebase safely
        let app;
        try {
            app = initializeApp(firebaseConfig);
            console.log("Firebase initialized successfully");

            // Set up global variables
            window.db = getFirestore(app);
            window.auth = getAuth(app);
        } catch (e) {
            if (e.code === 'app/duplicate-app') {
                console.log("Firebase already initialized, using existing app");
                try {
                    app = initializeApp();
                    window.db = getFirestore(app);
                    window.auth = getAuth(app);
                } catch(getAppError) {
                    console.error("Could not get existing Firebase app instance.", getAppError);
                }
            } else {
                console.error("Firebase initialization error:", e);
            }
        }
    </script>

    <!-- Authentication Setup -->
    <script type="module">
        import { auth as importedAuth, signInWithGoogle, signOutUser, initializeAuth } from './js/auth.js';
        window.auth = window.auth || importedAuth;
        window.signInWithGoogle = signInWithGoogle;
        window.signOutUser = signOutUser;

        // Initialize authentication
        document.addEventListener('DOMContentLoaded', () => {
            initializeAuth();
        });
    </script>

    <!-- Firestore Data Operations -->
    <script type="module">
        import { saveTasksToFirestore, loadTasksFromFirestore, saveCompletedTaskToFirestore } from './js/firestore.js';
        import { initializeFirestoreData } from './js/initFirestoreData.js';

        // Make functions available globally
        window.saveTasksToFirestore = saveTasksToFirestore;
        window.loadTasksFromFirestore = loadTasksFromFirestore;
        window.saveCompletedTaskToFirestore = saveCompletedTaskToFirestore;
        window.initializeFirestoreData = initializeFirestoreData;

        // Initialize data when DOM is loaded
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(() => {
                if (typeof window.initializeFirestoreData === 'function') {
                    window.initializeFirestoreData();
                }
            }, 1500);
        });
    </script>

    
    
    
    
    
    

