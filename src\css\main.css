/* Base Styles */
:root {
    /* Light theme variables */
    --primary-color: #4A90E2;
    --secondary-color: #2ECC71;
    --accent-color: #F39C12;
    --text-color: #2C3E50;
    --background-color: #F5F7FA;
    --card-background: #FFFFFF;
    --border-color: #E1E4E8;
    --shadow-color: rgba(0, 0, 0, 0.1);
    --hover-color: #F8F9FA;
    --transition-speed: 0.3s;
}

[data-theme="dark"] {
    --primary-color: #5C9CE6;
    --secondary-color: #3EDB81;
    --accent-color: #FFA726;
    --text-color: #E1E1E1;
    --background-color: #1A1A1A;
    --card-background: #2D2D2D;
    --border-color: #404040;
    --shadow-color: rgba(0, 0, 0, 0.3);
    --hover-color: #363636;
}

/* Theme Toggle Button */
.theme-toggle {
    position: fixed;
    top: 20px;
    right: 20px;
    background: var(--card-background);
    border: 1px solid var(--border-color);
    color: var(--text-color);
    padding: 8px 16px;
    border-radius: 20px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    z-index: 1000;
    box-shadow: 0 2px 5px var(--shadow-color);
}

.theme-toggle:hover {
    background: var(--hover-color);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    transition: all var(--transition-speed) ease-in-out;
}

body {
    font-family: 'Segoe UI', system-ui, -apple-system, sans-serif;
    margin: 0;
    padding: 0;
    padding-top: 60px; /* Match extracted.html */
    background-color: var(--background-color);
    color: var(--text-color);
    line-height: 1.6;
}

/* Header Styles */
.main-header {
    text-align: center;
    padding: 2rem 0;
}

.main-header h1 {
    font-size: 3.5rem;
    color: var(--primary-color);
    margin: 0;
    letter-spacing: -1px;
}

.tagline {
    font-size: 1.2rem;
    color: #666;
    margin-top: 0.5rem;
}

.logo-section {
    margin-bottom: 2rem;
}

/* Container and Layout */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* Button Styles */
.interactive-button {
    background-color: var(--primary-color);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 25px;
    font-size: 1.1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 5px var(--shadow-color);
}

.interactive-button:hover {
    background-color: #357ABD;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px var(--shadow-color);
}

.interactive-button.connected {
    background-color: var(--secondary-color);
}

/* Sound Controls */
.sound-controls {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
}

.sound-toggle {
    background: var(--card-background);
    border: 1px solid var(--border-color);
    padding: 10px;
    border-radius: 50%;
    cursor: pointer;
    box-shadow: 0 2px 5px var(--shadow-color);
    transition: all 0.3s ease;
}

.sound-toggle:hover {
    transform: scale(1.1);
}

/* Tasks Container */
.tasks-container {
    margin-top: 2rem;
    padding: 1rem;
    background: var(--card-background);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    box-shadow: 0 2px 4px var(--shadow-color);
}

.tasks-container.hidden {
    display: none;
}

.tasks-list {
    margin-top: 1rem;
}

.task-item {
    padding: 1rem;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: background-color 0.2s ease;
}

.task-item:hover {
    background-color: var(--hover-color);
}

.task-checkbox {
    width: 20px;
    height: 20px;
    border: 2px solid var(--primary-color);
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.task-checkbox.checked {
    background-color: var(--primary-color);
    position: relative;
}

.task-checkbox.checked::after {
    content: '✓';
    color: white;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.task-content {
    flex: 1;
}

.task-content.completed {
    text-decoration: line-through;
    color: #888;
}

.task-due-date {
    font-size: 0.9em;
    color: #666;
}

.task-priority {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 8px;
}

.priority-1 { background-color: #808080; }
.priority-2 { background-color: #5297ff; }
.priority-3 { background-color: #ff9a14; }
.priority-4 { background-color: #ff4646; }

/* Image Analysis Styles */
.upload-section {
    margin: 20px 0;
    padding: 20px;
    background: var(--card-background);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    box-shadow: 0 2px 4px var(--shadow-color);
}

.file-upload-container {
    width: 100%;
    max-width: 600px;
    margin: 0 auto;
}

.upload-box {
    border: 2px dashed #ccc;
    border-radius: 8px;
    padding: 40px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.upload-box:hover {
    border-color: #007bff;
    background: var(--hover-color);
}

.upload-box i {
    font-size: 48px;
    color: #6c757d;
    margin-bottom: 15px;
}

.image-preview {
    margin-top: 20px;
    text-align: center;
}

.image-preview img {
    max-width: 100%;
    max-height: 400px;
    border-radius: 8px;
    box-shadow: 0 2px 4px var(--shadow-color);
}

.analysis-results {
    background: var(--card-background);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: 20px;
    margin-top: 20px;
    box-shadow: 0 4px 6px var(--shadow-color);
}

.schedule-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-top: 15px;
}

.day-schedule {
    background: var(--card-background);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 15px;
    transition: transform 0.2s;
}

.day-schedule:hover {
    transform: translateY(-2px);
}

.day-schedule h6 {
    color: var(--text-color);
    margin-bottom: 10px;
    font-weight: 600;
}

.time-slots {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.time-slot {
    padding: 10px;
    border-radius: 6px;
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.time-slot.class {
    background: #e3f2fd;
    border-left: 4px solid #1976d2;
}

.time-slot.free {
    background: #f1f8e9;
    border-left: 4px solid #689f38;
}

.time-slot .time {
    font-size: 0.9em;
    color: #546e7a;
}

.time-slot .subject {
    font-weight: 500;
    color: #1976d2;
}

.time-slot .free-time {
    font-weight: 500;
    color: #689f38;
}

.free-time-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.day-free-time {
    background: var(--card-background);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 15px;
    box-shadow: 0 2px 4px var(--shadow-color);
}

.day-free-time h6 {
    color: var(--text-color);
    margin-bottom: 10px;
}

.stats {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.stat {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.stat .label {
    color: #546e7a;
    font-size: 0.9em;
}

.stat .value {
    font-weight: 500;
    color: var(--text-color);
}

.prime-slots {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.prime-slot {
    background: #f1f8e9;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 0.9em;
    color: #689f38;
}

.weekly-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-top: 20px;
}

.stat-card {
    background: var(--card-background);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 15px;
    box-shadow: 0 2px 4px var(--shadow-color);
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.stat-card .label {
    color: #546e7a;
    font-size: 0.9em;
}

.stat-card .value {
    font-size: 1.1em;
    font-weight: 600;
    color: var(--text-color);
}

.stat-card.best-days .value {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
}

.study-day {
    background: #e3f2fd;
    color: #1976d2;
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 0.9em;
}

.recommendations {
    margin-top: 20px;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.study-tips, .break-tips {
    background: var(--card-background);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 15px;
    box-shadow: 0 2px 4px var(--shadow-color);
}

.study-tips h6, .break-tips h6 {
    color: var(--text-color);
    margin-bottom: 10px;
}

.study-tips ul, .break-tips ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.study-tips li, .break-tips li {
    padding: 8px 0;
    border-bottom: 1px solid #f1f1f1;
    color: #546e7a;
    font-size: 0.95em;
}

.study-tips li:last-child, .break-tips li:last-child {
    border-bottom: none;
}

/* Navigation Styles */
.top-nav {
    background-color: var(--nav-bg, var(--card-background));
    padding: 10px 30px; /* Match extracted.html */
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3); /* Match extracted.html */
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: fixed; /* Match extracted.html */
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    height: 60px; /* Match extracted.html */
    backdrop-filter: blur(10px); /* Match extracted.html */
}

.nav-brand {
    font-size: 1.5rem;
    font-weight: bold;
    color: var(--primary-color);
}

.nav-links {
    display: flex;
    gap: 20px;
}

.nav-links a {
    color: var(--text-color);
    text-decoration: none;
    padding: 5px 10px; /* Match extracted.html */
    border-radius: 5px; /* Match extracted.html */
    transition: background-color 0.3s;
    font-size: 16px; /* Match extracted.html */
}

.nav-links a:hover {
    background-color: var(--hover-bg, var(--hover-color));
}

.nav-links a.active {
    background-color: var(--primary-color);
    color: white;
}

/* Timeline Styles */
.timeline-container {
    background: var(--card-background);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: 20px;
    margin-top: 20px;
    box-shadow: 0 4px 6px var(--shadow-color);
}

.timeline-controls {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 20px;
    margin-bottom: 20px;
}

.timeline-nav {
    background: none;
    border: none;
    color: var(--primary-color);
    cursor: pointer;
    padding: 8px;
    border-radius: 50%;
    transition: background-color 0.2s;
}

.timeline-nav:hover {
    background: var(--hover-color);
}

#currentDay {
    font-weight: 600;
    color: var(--text-color);
    min-width: 100px;
    text-align: center;
}

.timeline-grid {
    display: grid;
    grid-template-columns: repeat(48, 1fr);
    gap: 1px;
    background: var(--background-color);
    padding: 10px;
    border-radius: 8px;
    position: relative;
    min-height: 60px;
}

.time-block {
    height: 40px;
    transition: all 0.2s;
    position: relative;
}

.time-block:hover {
    transform: translateY(-2px);
}

.time-block.class-time {
    background: var(--primary-color);
}

.time-block.free-time {
    background: #689f38;
}

.time-block.buffer-time {
    background: #ff9800;
}

.time-block::before {
    content: attr(data-time);
    position: absolute;
    bottom: -20px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 0.7em;
    color: #546e7a;
    white-space: nowrap;
}

.timeline-legend {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-top: 30px;
    padding-top: 10px;
    border-top: 1px solid var(--border-color);
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 8px;
}

.legend-color {
    width: 20px;
    height: 20px;
    border-radius: 4px;
}

.legend-color.class-time {
    background: var(--primary-color);
}

.legend-color.free-time {
    background: #689f38;
}

.legend-color.buffer-time {
    background: #ff9800;
}

/* Schedule Config Styles */
.schedule-config {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin: 20px 0;
}

.time-input-group {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.time-input-group label {
    font-weight: 500;
    color: var(--text-color);
}

.time-input-group input[type="time"] {
    padding: 8px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    font-size: 1em;
}

.time-input-group select {
    padding: 8px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    font-size: 0.9em;
    color: #546e7a;
}

/* Account Section Styles */
.account-section {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: var(--card-background, #ffffff);
    border-radius: 12px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.user-profile {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid var(--primary-color);
}

.user-details {
    display: flex;
    flex-direction: column;
}

.user-name {
    font-weight: 600;
    color: var(--text-color);
}

.user-email {
    font-size: 0.875rem;
    color: var(--text-secondary, #666);
}

.account-status {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    color: var(--text-secondary, #666);
}

.status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: var(--error-color, #ff4444);
}

.status-indicator.connected {
    background-color: var(--success-color, #2ecc71);
}

.login-btn {
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    background-color: var(--primary-color);
    color: white;
    border: none;
}

.login-btn:hover {
    background-color: var(--primary-color-dark);
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.logout-btn {
    padding: 0.375rem 0.75rem; /* 6px 12px */
    border-radius: 0.25rem; /* 4px */
    background: rgba(255, 255, 255, 0.15); /* Slightly transparent white */
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.3);
    font-size: 0.875rem; /* 14px */
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    backdrop-filter: blur(5px); /* Glass effect */
    -webkit-backdrop-filter: blur(5px); /* For Safari */
}

.logout-btn:hover {
    background: rgba(255, 255, 255, 0.25);
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .account-section {
        flex-direction: column;
        align-items: stretch;
        gap: 0.75rem;
    }

    .user-profile {
        justify-content: center;
    }

    .account-status {
        justify-content: center;
    }

    .login-btn, .logout-btn {
        width: 100%;
    }
}

/* Animation classes */
.fade-in {
    animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .daily-free-time {
        grid-template-columns: 1fr;
    }

    .recommendations-container {
        grid-template-columns: 1fr;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }

    .main-header h1 {
        font-size: 2.5rem;
    }

    .tagline {
        font-size: 1rem;
    }
}
