../js/common.js
../js/cross-tab-sync.js
../js/sideDrawer.js
../js/tasksManager.js
../js/transitionManager.js
/js/inject-header.js
/socket.io/socket.io.js
https://accounts.google.com/gsi/client
https://apis.google.com/js/api.js
https://cdn.jsdelivr.net/npm/@popperjs/core@2.11.8/dist/umd/popper.min.js
https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js
https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js
https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js
https://cdn.jsdelivr.net/npm/chart.js
https://cdn.jsdelivr.net/npm/es-module-shims
https://cdn.jsdelivr.net/npm/marked/marked.min.js
https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js
https://cdn.quilljs.com/1.3.6/quill.min.js
https://cdnjs.cloudflare.com/ajax/libs/docx/7.8.2/docx.js
https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js
https://cdnjs.cloudflare.com/ajax/libs/html2pdf.js/0.10.1/html2pdf.bundle.min.js
https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js
https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.min.js
https://kit.fontawesome.com/51198d7b97.js
https://unpkg.com/es-module-shims@1.8.0/dist/es-module-shims.js
https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js
https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js
https://www.gstatic.com/firebasejs/8.10.0/firebase-app.js
https://www.gstatic.com/firebasejs/8.10.0/firebase-functions.js
js/academic-details.js
js/ai-latex-conversion.js
js/ai-researcher.js
js/alarm-mini-display.js
js/alarm-service.js
js/api-optimization.js
js/api-settings.js
js/apiSettingsManager.js
js/app.js
js/auth.js
js/cacheManager.js
js/calendarManager.js
js/clock-display.js
js/common.js
js/cross-tab-sync.js
js/currentTaskManager.js
js/data-sync-integration.js
js/energyLevels.js
js/firebase-init.js
js/firebaseAuth.js
js/firestore-global.js
js/firestore.js
js/flashcards.js
js/gemini-api.js
js/grind-speech-synthesis.js
js/imageAnalyzer.js
js/initFirestoreData.js
js/pomodoroTimer.js
js/priority-list-sorting.js
js/priority-list-utils.js
js/priority-sync-fix.js
js/quoteManager.js
js/roleModelManager.js
js/scheduleManager.js
js/semester-management.js
js/sideDrawer.js
js/sleep-saboteurs-init.js
js/sleepScheduleManager.js
js/sleepTimeCalculator.js
js/sm2.js
js/soundManager.js
js/speech-recognition.js
js/speech-synthesis.js
js/storageManager.js
js/studySpaceAnalyzer.js
js/studySpacesManager.js
js/subject-management.js
js/subject-marks-integration.js
js/subject-marks-ui.js
js/task-notes-injector.js
js/task-notes.js
js/taskFilters.js
js/taskLinks.js
js/tasksManager.js
js/test-feedback.js
js/text-expansion.js
js/theme-manager.js
js/theme-toggle.js
js/themeManager.js
js/timetableAnalyzer.js
js/timetableIntegration.js
js/todoistIntegration.js
js/transitionManager.js
js/ui-utilities.js
js/userGuidance.js
js/workspace-attachments.js
js/workspace-core.js
js/workspace-document.js
js/workspace-formatting.js
js/workspace-media.js
js/workspace-tables-links.js
js/workspace-ui.js
js/workspaceFlashcardIntegration.js
priority-calculator.js
script.js
