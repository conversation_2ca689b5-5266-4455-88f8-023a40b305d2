/**
 * GPAce Application Entry Point
 * Main module that orchestrates the application initialization
 */

// Import utility modules
import { toggleTheme, initializeTheme, setupPeriodicSync } from './utils/ui-utilities.js';
import storageManager from './utils/storageManager.js';
import { initAlarmService } from './utils/alarm-service.js';
import { initFirebaseConfig } from './utils/firebase-config.js';

// Import core functionality
import { initSideDrawer } from './sideDrawer.js';
import { initCommonHeader } from './common-header.js';
import { initCrossTabSync } from './cross-tab-sync.js';
import { initNavigation } from './components/navigation.js';

// Application state
class GPAceApp {
    constructor() {
        this.initialized = false;
        this.modules = new Map();
        this.storage = storageManager;
        this.router = null;
    }

    /**
     * Initialize the application
     */
    async init() {
        if (this.initialized) {
            console.warn('GPAce app already initialized');
            return;
        }

        console.log('🚀 Initializing GPAce Application...');

        try {
            // Initialize core utilities
            await this.initCore();
            
            // Initialize UI components
            await this.initUI();
            
            // Initialize data and services
            await this.initServices();
            
            // Initialize router
            await this.initRouter();

            this.initialized = true;
            console.log('✅ GPAce Application initialized successfully');

            // Dispatch custom event for other modules
            window.dispatchEvent(new CustomEvent('gpace:initialized'));

        } catch (error) {
            console.error('❌ Failed to initialize GPAce Application:', error);
            throw error;
        }
    }

    /**
     * Initialize core utilities and theme
     */
    async initCore() {
        console.log('🔧 Initializing core utilities...');
        
        // Initialize theme
        initializeTheme();
        
        // Setup periodic sync
        setupPeriodicSync();
        
        // Initialize Firebase if available
        try {
            await initFirebaseConfig();
        } catch (error) {
            console.warn('Firebase initialization failed:', error);
        }
    }

    /**
     * Initialize UI components
     */
    async initUI() {
        console.log('🎨 Initializing UI components...');
        
        // Initialize side drawer
        try {
            initSideDrawer();
        } catch (error) {
            console.warn('Side drawer initialization failed:', error);
        }

        // Initialize common header
        try {
            initCommonHeader();
        } catch (error) {
            console.warn('Common header initialization failed:', error);
        }

        // Initialize cross-tab sync
        try {
            initCrossTabSync();
        } catch (error) {
            console.warn('Cross-tab sync initialization failed:', error);
        }

        // Initialize navigation
        try {
            initNavigation();
        } catch (error) {
            console.warn('Navigation initialization failed:', error);
        }
    }

    /**
     * Initialize services
     */
    async initServices() {
        console.log('⚙️ Initializing services...');
        
        // Initialize alarm service
        try {
            await initAlarmService();
        } catch (error) {
            console.warn('Alarm service initialization failed:', error);
        }
        
        // Ensure data is initialized
        await this.ensureDataInitialized();
    }

    /**
     * Initialize page-specific modules based on current page
     * Can be called by router for dynamic page loading
     */
    async initPageModules(pageName = null) {
        const currentPage = pageName || this.getCurrentPage();
        console.log(`📄 Initializing modules for page: ${currentPage}`);

        try {
            switch (currentPage) {
                case 'grind':
                    await this.loadGrindModules();
                    break;
                case 'tasks':
                    await this.loadTaskModules();
                    break;
                case 'workspace':
                    await this.loadWorkspaceModules();
                    break;
                case 'flashcards':
                    await this.loadFlashcardModules();
                    break;
                case 'academic-details':
                    await this.loadAcademicModules();
                    break;
                case 'study-spaces':
                    await this.loadStudySpaceModules();
                    break;
                case 'daily-calendar':
                    await this.loadCalendarModules();
                    break;
                case 'instant-test-feedback':
                    await this.loadTestFeedbackModules();
                    break;
                case 'extracted':
                    await this.loadExtractedModules();
                    break;
                case 'subject-marks':
                    await this.loadSubjectMarksModules();
                    break;
                case 'settings':
                    await this.loadSettingsModules();
                    break;
                case 'priority-calculator':
                    await this.loadPriorityModules();
                    break;
                case 'sleep-saboteurs':
                    await this.loadSleepModules();
                    break;
                case 'landing':
                    await this.loadLandingModules();
                    break;
                default:
                    console.log('No specific modules for this page');
            }
        } catch (error) {
            console.error(`Failed to load modules for page ${currentPage}:`, error);
        }
    }

    /**
     * Initialize router
     */
    async initRouter() {
        console.log('🔀 Initializing router...');
        this.router = new GPAceRouter(this);
        this.router.init();

        // Make router available globally
        window.router = this.router;
        window.navigate = (path) => this.router.navigate(path);
    }

    /**
     * Get current page name from URL or document
     */
    getCurrentPage() {
        const path = window.location.pathname;
        const filename = path.split('/').pop().replace('.html', '');
        return filename || 'landing';
    }

    /**
     * Ensure data is initialized
     */
    async ensureDataInitialized() {
        console.log('🔄 Checking data initialization...');
        
        const dataInitialized = this.storage.getItem('dataInitialized') === 'true';
        const subjects = this.storage.getItem('academicSubjects', []);
        
        if (!dataInitialized || subjects.length === 0) {
            console.log('📥 Data not initialized, starting initialization...');
            if (typeof window.initializeFirestoreData === 'function') {
                await window.initializeFirestoreData();
            }
        } else {
            console.log('✅ Data already initialized');
            
            // Still update priority tasks to ensure they're current
            if (typeof window.updatePriorityTasks === 'function') {
                await window.updatePriorityTasks();
            }
        }
    }

    /**
     * Load modules for grind page
     */
    async loadGrindModules() {
        console.log('Loading grind page modules...');
        // Dynamic imports for grind-specific modules
        // These will be implemented as we convert more modules
    }

    /**
     * Load modules for task page
     */
    async loadTaskModules() {
        console.log('Loading task page modules...');
        // Dynamic imports for task-specific modules
    }

    /**
     * Load modules for workspace page
     */
    async loadWorkspaceModules() {
        console.log('Loading workspace page modules...');
        // Dynamic imports for workspace-specific modules
    }

    /**
     * Load modules for flashcard page
     */
    async loadFlashcardModules() {
        console.log('Loading flashcard page modules...');
        // Dynamic imports for flashcard-specific modules
    }

    /**
     * Load modules for academic details page
     */
    async loadAcademicModules() {
        console.log('Loading academic page modules...');
        // Dynamic imports for academic-specific modules
    }

    /**
     * Load modules for study spaces page
     */
    async loadStudySpaceModules() {
        console.log('Loading study space page modules...');
        // Dynamic imports for study space-specific modules
    }

    /**
     * Load modules for calendar page
     */
    async loadCalendarModules() {
        console.log('Loading calendar page modules...');
        // Dynamic imports for calendar-specific modules
    }

    /**
     * Load modules for test feedback page
     */
    async loadTestFeedbackModules() {
        console.log('Loading test feedback page modules...');
        // Dynamic imports for test feedback-specific modules
    }

    /**
     * Load modules for extracted page
     */
    async loadExtractedModules() {
        console.log('Loading extracted page modules...');
        // Dynamic imports for extracted-specific modules
    }

    /**
     * Load modules for subject marks page
     */
    async loadSubjectMarksModules() {
        console.log('Loading subject marks page modules...');
        // Dynamic imports for subject marks-specific modules
    }

    /**
     * Load modules for settings page
     */
    async loadSettingsModules() {
        console.log('Loading settings page modules...');
        // Dynamic imports for settings-specific modules
    }

    /**
     * Load modules for priority calculator page
     */
    async loadPriorityModules() {
        console.log('Loading priority calculator page modules...');
        // Dynamic imports for priority-specific modules
    }

    /**
     * Load modules for sleep saboteurs page
     */
    async loadSleepModules() {
        console.log('Loading sleep saboteurs page modules...');
        // Dynamic imports for sleep-specific modules
    }

    /**
     * Load modules for landing page
     */
    async loadLandingModules() {
        console.log('Loading landing page modules...');
        // Dynamic imports for landing-specific modules
    }

    /**
     * Register a module
     */
    registerModule(name, module) {
        this.modules.set(name, module);
        console.log(`📦 Module registered: ${name}`);
    }

    /**
     * Get a registered module
     */
    getModule(name) {
        return this.modules.get(name);
    }
}

// Create global app instance
const app = new GPAceApp();

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', async () => {
    try {
        await app.init();
    } catch (error) {
        console.error('Failed to initialize application:', error);
    }
});

// Listen for storage changes
window.addEventListener('storage', async (e) => {
    if (e.key === 'academicSubjects' || e.key?.startsWith('tasks-')) {
        console.log('🔄 Storage changed, updating data...');
        await app.ensureDataInitialized();
    }
});

/**
 * Simple Client-Side Router
 */
class GPAceRouter {
    constructor(app) {
        this.app = app;
        this.routes = new Map();
        this.currentRoute = null;
        this.setupDefaultRoutes();
        this.setupEventListeners();
    }

    setupDefaultRoutes() {
        // Define application routes
        this.addRoute('/', () => this.loadPage('landing'));
        this.addRoute('/landing', () => this.loadPage('landing'));
        this.addRoute('/grind', () => this.loadPage('grind'));
        this.addRoute('/tasks', () => this.loadPage('tasks'));
        this.addRoute('/workspace', () => this.loadPage('workspace'));
        this.addRoute('/flashcards', () => this.loadPage('flashcards'));
        this.addRoute('/academic-details', () => this.loadPage('academic-details'));
        this.addRoute('/study-spaces', () => this.loadPage('study-spaces'));
        this.addRoute('/daily-calendar', () => this.loadPage('daily-calendar'));
        this.addRoute('/instant-test-feedback', () => this.loadPage('instant-test-feedback'));
        this.addRoute('/extracted', () => this.loadPage('extracted'));
        this.addRoute('/subject-marks', () => this.loadPage('subject-marks'));
        this.addRoute('/settings', () => this.loadPage('settings'));
        this.addRoute('/priority-calculator', () => this.loadPage('priority-calculator'));
        this.addRoute('/priority-list', () => this.loadPage('priority-list'));
        this.addRoute('/sleep-saboteurs', () => this.loadPage('sleep-saboteurs'));
        this.addRoute('/404', () => this.loadErrorPage(404));
    }

    addRoute(path, handler) {
        this.routes.set(path, handler);
    }

    setupEventListeners() {
        // Handle browser back/forward buttons
        window.addEventListener('popstate', () => {
            this.handleRoute();
        });

        // Handle link clicks
        document.addEventListener('click', (e) => {
            if (e.target.matches('a[href^="/"], a[href^="#/"]')) {
                e.preventDefault();
                const href = e.target.getAttribute('href').replace('#', '');
                this.navigate(href);
            }
        });
    }

    navigate(path) {
        if (path !== this.currentRoute) {
            history.pushState(null, '', path);
            this.handleRoute();
        }
    }

    handleRoute() {
        const path = window.location.pathname;
        const handler = this.routes.get(path) || this.routes.get('/404');

        if (handler) {
            this.currentRoute = path;
            handler();
        } else {
            this.navigate('/404');
        }
    }

    async loadPage(pageName) {
        try {
            console.log(`📄 Loading page: ${pageName}`);

            // Load page content
            const response = await fetch(`./src/pages/${pageName}.html`);
            if (!response.ok) {
                throw new Error(`Failed to load page: ${response.status}`);
            }

            const html = await response.text();

            // Extract body content (remove html, head, body tags)
            const bodyMatch = html.match(/<body[^>]*>([\s\S]*?)<\/body>/i);
            const content = bodyMatch ? bodyMatch[1] : html;

            // Update page content
            document.getElementById('root').innerHTML = content;

            // Update page title
            const titleMatch = html.match(/<title[^>]*>([\s\S]*?)<\/title>/i);
            if (titleMatch) {
                document.title = titleMatch[1];
            }

            // Initialize page-specific modules
            await this.app.initPageModules(pageName);

            // Trigger page loaded event
            window.dispatchEvent(new CustomEvent('pageLoaded', { detail: { page: pageName } }));

        } catch (error) {
            console.error('Error loading page:', error);
            this.loadErrorPage(500);
        }
    }

    loadErrorPage(code) {
        const errorMessages = {
            404: 'Page Not Found',
            500: 'Internal Server Error'
        };

        const message = errorMessages[code] || 'Unknown Error';

        document.getElementById('root').innerHTML = `
            <div class="error-container">
                <div class="error-code">${code}</div>
                <div class="error-message">${message}</div>
                <div class="error-actions">
                    <a href="/" class="btn btn-primary">Go Home</a>
                    <a href="/grind" class="btn btn-primary">Grind Mode</a>
                </div>
            </div>
        `;
    }

    init() {
        // Handle initial route
        this.handleRoute();
    }
}

// Export app instance for global access
window.GPAceApp = app;
export default app;
