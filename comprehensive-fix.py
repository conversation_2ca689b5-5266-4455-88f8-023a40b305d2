#!/usr/bin/env python3
"""
COMPREHENSIVE Post-Reorganization Fix Tool
This tool addresses EVERYTHING that could be broken, not just obvious references
"""

import os
import re
import json
import yaml
from pathlib import Path
from typing import List, Dict, Set
import argparse

class ComprehensiveFixer:
    def __init__(self, root_dir: str = "."):
        self.root = Path(root_dir).resolve()
        self.issues = []
        self.critical_issues = []
        self.fixes_applied = []
        
        # Comprehensive path mappings
        self.path_mappings = {
            "assets/": "src/assets/",
            "css/": "src/css/",
            "js/": "src/js/",
            "data/": "src/data/",
            "sounds/": "src/assets/audio/sounds/",
            "alarm-sounds/": "src/assets/audio/alarms/",
            "relaxed-mode/": "src/pages/",
            "styles/": "src/css/",
            "scripts/": "src/js/",
        }

    def scan_build_configs(self):
        """Scan build system configurations"""
        print("🔍 Scanning build configurations...")
        
        build_configs = [
            "webpack.config.js", "webpack.config.ts",
            "vite.config.js", "vite.config.ts", 
            "rollup.config.js", "rollup.config.ts",
            "esbuild.config.js", "parcel.config.js",
            "gulpfile.js", "Gruntfile.js"
        ]
        
        for config_file in build_configs:
            config_path = self.root / config_file
            if config_path.exists():
                self.scan_js_config_file(config_path, "BUILD_CONFIG")

    def scan_deployment_configs(self):
        """Scan deployment and hosting configurations"""
        print("🔍 Scanning deployment configurations...")
        
        # Firebase
        firebase_json = self.root / "firebase.json"
        if firebase_json.exists():
            self.scan_json_config(firebase_json, "FIREBASE")
            
        # Netlify
        netlify_toml = self.root / "netlify.toml"
        if netlify_toml.exists():
            self.scan_toml_config(netlify_toml, "NETLIFY")
            
        # Vercel
        vercel_json = self.root / "vercel.json"
        if vercel_json.exists():
            self.scan_json_config(vercel_json, "VERCEL")
            
        # Docker
        dockerfile = self.root / "Dockerfile"
        if dockerfile.exists():
            self.scan_dockerfile(dockerfile)
            
        docker_compose = self.root / "docker-compose.yml"
        if docker_compose.exists():
            self.scan_yaml_config(docker_compose, "DOCKER_COMPOSE")

    def scan_server_configs(self):
        """Scan server configuration files"""
        print("🔍 Scanning server configurations...")
        
        server_configs = [
            ".htaccess", "nginx.conf", "apache.conf",
            "web.config", "server.js", "app.js"
        ]
        
        for config_file in server_configs:
            config_path = self.root / config_file
            if config_path.exists():
                self.scan_text_file(config_path, "SERVER_CONFIG")

    def scan_pwa_configs(self):
        """Scan PWA and Service Worker configurations"""
        print("🔍 Scanning PWA configurations...")
        
        # Web App Manifest
        manifest_files = ["manifest.json", "site.webmanifest", "app.webmanifest"]
        for manifest_file in manifest_files:
            manifest_path = self.root / manifest_file
            if manifest_path.exists():
                self.scan_json_config(manifest_path, "PWA_MANIFEST")
        
        # Service Workers
        sw_files = list(self.root.rglob("*service-worker*.js")) + list(self.root.rglob("sw.js"))
        for sw_file in sw_files:
            self.scan_service_worker(sw_file)

    def scan_testing_configs(self):
        """Scan testing framework configurations"""
        print("🔍 Scanning testing configurations...")
        
        test_configs = [
            "jest.config.js", "jest.config.json",
            "cypress.config.js", "cypress.json",
            "playwright.config.js", "vitest.config.js",
            "karma.conf.js", "protractor.conf.js"
        ]
        
        for config_file in test_configs:
            config_path = self.root / config_file
            if config_path.exists():
                if config_file.endswith('.json'):
                    self.scan_json_config(config_path, "TEST_CONFIG")
                else:
                    self.scan_js_config_file(config_path, "TEST_CONFIG")

    def scan_ide_configs(self):
        """Scan IDE and editor configurations"""
        print("🔍 Scanning IDE configurations...")
        
        # VSCode
        vscode_settings = self.root / ".vscode" / "settings.json"
        if vscode_settings.exists():
            self.scan_json_config(vscode_settings, "VSCODE")
            
        # TypeScript
        tsconfig = self.root / "tsconfig.json"
        if tsconfig.exists():
            self.scan_json_config(tsconfig, "TYPESCRIPT")
            
        # ESLint
        eslint_configs = [".eslintrc.js", ".eslintrc.json", "eslint.config.js"]
        for eslint_file in eslint_configs:
            eslint_path = self.root / eslint_file
            if eslint_path.exists():
                if eslint_file.endswith('.json'):
                    self.scan_json_config(eslint_path, "ESLINT")
                else:
                    self.scan_js_config_file(eslint_path, "ESLINT")

    def scan_dynamic_imports(self):
        """Scan for dynamic imports and code splitting"""
        print("🔍 Scanning dynamic imports...")
        
        js_files = list(self.root.rglob("*.js")) + list(self.root.rglob("*.ts"))
        for js_file in js_files:
            if self.should_skip_file(js_file):
                continue
                
            try:
                content = js_file.read_text(encoding='utf-8')
                
                # Dynamic import patterns
                dynamic_patterns = [
                    r'import\s*\(\s*["\']([^"\']*)["\']',  # import('path')
                    r'new\s+Worker\s*\(\s*["\']([^"\']*)["\']',  # new Worker('path')
                    r'new\s+SharedWorker\s*\(\s*["\']([^"\']*)["\']',  # SharedWorker
                    r'importScripts\s*\(\s*["\']([^"\']*)["\']',  # importScripts
                ]
                
                for pattern in dynamic_patterns:
                    matches = re.findall(pattern, content)
                    for match in matches:
                        if self.is_potentially_broken_path(match, js_file):
                            self.log_issue("HIGH", str(js_file), 
                                         f"Dynamic import may be broken: {match}", "DYNAMIC_IMPORT")
                            
            except Exception as e:
                self.log_issue("ERROR", str(js_file), f"Cannot scan dynamic imports: {e}")

    def scan_css_advanced(self):
        """Advanced CSS scanning for @import, fonts, custom properties"""
        print("🔍 Scanning advanced CSS features...")
        
        css_files = list(self.root.rglob("*.css")) + list(self.root.rglob("*.scss")) + list(self.root.rglob("*.less"))
        for css_file in css_files:
            if self.should_skip_file(css_file):
                continue
                
            try:
                content = css_file.read_text(encoding='utf-8')
                
                # Advanced CSS patterns
                css_patterns = [
                    (r'@import\s+["\']([^"\']*)["\']', "@import statement"),
                    (r'@font-face\s*{[^}]*src:\s*url\(["\']?([^"\']*)["\']?\)', "font-face src"),
                    (r'--[^:]*:\s*url\(["\']?([^"\']*)["\']?\)', "CSS custom property"),
                    (r'background(?:-image)?:\s*url\(["\']?([^"\']*)["\']?\)', "background image"),
                ]
                
                for pattern, desc in css_patterns:
                    matches = re.findall(pattern, content, re.IGNORECASE | re.DOTALL)
                    for match in matches:
                        if isinstance(match, tuple):
                            match = match[0] if match[0] else match[1]
                        if self.is_potentially_broken_path(match, css_file):
                            self.log_issue("HIGH", str(css_file), 
                                         f"Broken {desc}: {match}", "CSS_ADVANCED")
                            
            except Exception as e:
                self.log_issue("ERROR", str(css_file), f"Cannot scan CSS: {e}")

    def scan_json_config(self, file_path: Path, config_type: str):
        """Scan JSON configuration files"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
                self.scan_nested_object(config, str(file_path), config_type)
        except Exception as e:
            self.log_issue("ERROR", str(file_path), f"Cannot parse JSON: {e}")

    def scan_yaml_config(self, file_path: Path, config_type: str):
        """Scan YAML configuration files"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
                self.scan_nested_object(config, str(file_path), config_type)
        except Exception as e:
            self.log_issue("ERROR", str(file_path), f"Cannot parse YAML: {e}")

    def scan_js_config_file(self, file_path: Path, config_type: str):
        """Scan JavaScript configuration files"""
        try:
            content = file_path.read_text(encoding='utf-8')
            
            # Look for path-like strings in JS configs
            path_patterns = [
                r'["\']([^"\']*(?:assets|css|js|data|sounds|alarm-sounds)[^"\']*)["\']',
                r'entry:\s*["\']([^"\']*)["\']',
                r'output:\s*["\']([^"\']*)["\']',
                r'publicPath:\s*["\']([^"\']*)["\']',
                r'contentBase:\s*["\']([^"\']*)["\']',
            ]
            
            for pattern in path_patterns:
                matches = re.findall(pattern, content)
                for match in matches:
                    if self.is_potentially_broken_path(match, file_path):
                        self.log_issue("CRITICAL", str(file_path), 
                                     f"Build config path may be broken: {match}", config_type)
                        
        except Exception as e:
            self.log_issue("ERROR", str(file_path), f"Cannot scan JS config: {e}")

    def scan_dockerfile(self, file_path: Path):
        """Scan Dockerfile for COPY/ADD commands"""
        try:
            content = file_path.read_text(encoding='utf-8')
            
            # Docker COPY/ADD patterns
            docker_patterns = [
                r'COPY\s+([^\s]+)\s+([^\s]+)',
                r'ADD\s+([^\s]+)\s+([^\s]+)',
            ]
            
            for pattern in docker_patterns:
                matches = re.findall(pattern, content)
                for src, dest in matches:
                    if any(old_path in src for old_path in self.path_mappings.keys()):
                        self.log_issue("CRITICAL", str(file_path), 
                                     f"Docker path may be broken: {src} -> {dest}", "DOCKER")
                        
        except Exception as e:
            self.log_issue("ERROR", str(file_path), f"Cannot scan Dockerfile: {e}")

    def scan_service_worker(self, file_path: Path):
        """Scan service worker files for cache paths"""
        try:
            content = file_path.read_text(encoding='utf-8')
            
            # Service worker cache patterns
            cache_patterns = [
                r'["\']([^"\']*(?:assets|css|js|data)[^"\']*)["\']',
                r'urlsToCache\s*=\s*\[(.*?)\]',
                r'CACHE_URLS\s*=\s*\[(.*?)\]',
            ]
            
            for pattern in cache_patterns:
                matches = re.findall(pattern, content, re.DOTALL)
                for match in matches:
                    if self.is_potentially_broken_path(match, file_path):
                        self.log_issue("CRITICAL", str(file_path), 
                                     f"Service worker cache path broken: {match}", "SERVICE_WORKER")
                        
        except Exception as e:
            self.log_issue("ERROR", str(file_path), f"Cannot scan service worker: {e}")

    def scan_text_file(self, file_path: Path, config_type: str):
        """Scan generic text files for path references"""
        try:
            content = file_path.read_text(encoding='utf-8')
            
            # Generic path patterns
            for old_path in self.path_mappings.keys():
                if old_path in content:
                    self.log_issue("MEDIUM", str(file_path), 
                                 f"Path reference found: {old_path}", config_type)
                    
        except Exception as e:
            self.log_issue("ERROR", str(file_path), f"Cannot scan text file: {e}")

    def scan_nested_object(self, obj, file_path: str, config_type: str, path: str = ""):
        """Recursively scan nested objects for path references"""
        if isinstance(obj, dict):
            for key, value in obj.items():
                current_path = f"{path}.{key}" if path else key
                if isinstance(value, str) and self.looks_like_path(value):
                    if self.is_potentially_broken_path(value, Path(file_path)):
                        self.log_issue("HIGH", file_path, 
                                     f"Config path may be broken in {current_path}: {value}", config_type)
                else:
                    self.scan_nested_object(value, file_path, config_type, current_path)
        elif isinstance(obj, list):
            for i, item in enumerate(obj):
                self.scan_nested_object(item, file_path, config_type, f"{path}[{i}]")

    def is_potentially_broken_path(self, path_str: str, from_file: Path) -> bool:
        """Check if a path might be broken due to reorganization"""
        if not path_str or not isinstance(path_str, str):
            return False
            
        # Skip external URLs and special paths
        if path_str.startswith(('http://', 'https://', 'data:', '#', '//', 'mailto:', 'tel:')):
            return False
            
        # Check if path contains any of our moved directories
        return any(old_path in path_str for old_path in self.path_mappings.keys())

    def looks_like_path(self, value: str) -> bool:
        """Check if a string looks like a file path"""
        if not isinstance(value, str):
            return False
        return ('/' in value or '\\' in value) and not value.startswith(('http://', 'https://', 'data:'))

    def should_skip_file(self, file_path: Path) -> bool:
        """Check if file should be skipped"""
        skip_patterns = ['.git', 'node_modules', '.backup', '__pycache__', '.pytest_cache']
        return any(pattern in str(file_path) for pattern in skip_patterns)

    def log_issue(self, severity: str, file_path: str, issue: str, category: str = "GENERAL"):
        """Log an issue"""
        issue_obj = {
            "severity": severity,
            "file": file_path,
            "issue": issue,
            "category": category,
            "fixed": False
        }
        
        if severity == "CRITICAL":
            self.critical_issues.append(issue_obj)
        else:
            self.issues.append(issue_obj)

    def run_comprehensive_scan(self):
        """Run all scanning methods"""
        print("🚀 Starting COMPREHENSIVE diagnosis...")
        print("This will find EVERYTHING that could be broken...")
        
        self.scan_build_configs()
        self.scan_deployment_configs()
        self.scan_server_configs()
        self.scan_pwa_configs()
        self.scan_testing_configs()
        self.scan_ide_configs()
        self.scan_dynamic_imports()
        self.scan_css_advanced()
        
        total_issues = len(self.issues) + len(self.critical_issues)
        print(f"\n📊 Comprehensive scan complete: {total_issues} potential issues found")

    def print_comprehensive_report(self):
        """Print detailed comprehensive report"""
        print("\n" + "="*80)
        print("🔍 COMPREHENSIVE REORGANIZATION ISSUES REPORT")
        print("="*80)
        
        if self.critical_issues:
            print(f"\n🚨 CRITICAL ISSUES ({len(self.critical_issues)}):")
            print("These WILL break your application:")
            for i, issue in enumerate(self.critical_issues, 1):
                print(f"\n{i}. 🔴 CRITICAL - {issue['category']}")
                print(f"   File: {issue['file']}")
                print(f"   Issue: {issue['issue']}")
        
        if self.issues:
            print(f"\n⚠️  OTHER ISSUES ({len(self.issues)}):")
            categories = {}
            for issue in self.issues:
                cat = issue['category']
                if cat not in categories:
                    categories[cat] = []
                categories[cat].append(issue)
            
            for category, cat_issues in categories.items():
                print(f"\n📂 {category} ({len(cat_issues)} issues):")
                for issue in cat_issues[:3]:  # Show first 3
                    print(f"   • {issue['issue']}")
                if len(cat_issues) > 3:
                    print(f"   ... and {len(cat_issues) - 3} more")
        
        if not self.critical_issues and not self.issues:
            print("\n✅ No critical issues found!")
            print("Your reorganization appears to be safe.")

def main():
    parser = argparse.ArgumentParser(description="Comprehensive reorganization issue detector")
    parser.add_argument("--root", default=".", help="Root directory to scan")
    
    args = parser.parse_args()
    
    fixer = ComprehensiveFixer(args.root)
    fixer.run_comprehensive_scan()
    fixer.print_comprehensive_report()

if __name__ == "__main__":
    main()
