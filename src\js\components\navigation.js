/**
 * Navigation Component
 * Provides consistent navigation across all pages
 */

export class Navigation {
    constructor() {
        this.currentPage = null;
        this.init();
    }

    init() {
        this.createNavigation();
        this.setupEventListeners();
    }

    createNavigation() {
        // Check if navigation already exists
        if (document.querySelector('.top-nav')) {
            this.updateActiveState();
            return;
        }

        const nav = document.createElement('nav');
        nav.className = 'top-nav';
        nav.innerHTML = `
            <div class="nav-brand d-flex align-items-center">
                <img src="src/assets/images/gpace-logo-white.png" alt="GPAce Logo" style="height: 60px; margin-right: 0px;">
                <a href="/" onclick="event.preventDefault(); window.navigate('/')" style="text-decoration: none; color: inherit;">GPAce</a>
            </div>
            <div class="nav-links">
                <a href="/grind" onclick="event.preventDefault(); window.navigate('/grind')" data-page="grind">Grind Mode</a>
                <a href="/instant-test-feedback" onclick="event.preventDefault(); window.navigate('/instant-test-feedback')" data-page="instant-test-feedback">Test Feedback</a>
                <a href="/study-spaces" onclick="event.preventDefault(); window.navigate('/study-spaces')" data-page="study-spaces">Grind Station</a>
                <a href="/daily-calendar" onclick="event.preventDefault(); window.navigate('/daily-calendar')" data-page="daily-calendar">Daily Drip</a>
                <a href="/academic-details" onclick="event.preventDefault(); window.navigate('/academic-details')" data-page="academic-details">Brain Juice</a>
                <a href="/extracted" onclick="event.preventDefault(); window.navigate('/extracted')" data-page="extracted">Hustle Hub</a>
                <a href="/subject-marks" onclick="event.preventDefault(); window.navigate('/subject-marks')" data-page="subject-marks">Subject Marks</a>
                <a href="/flashcards" onclick="event.preventDefault(); window.navigate('/flashcards')" data-page="flashcards">Flashcards</a>
                <button class="drawer-toggle">
                    <i class="bi bi-gear"></i>
                </button>
            </div>
        `;

        // Insert navigation at the beginning of the page
        const root = document.getElementById('root');
        if (root && root.firstChild) {
            root.insertBefore(nav, root.firstChild);
        } else if (root) {
            root.appendChild(nav);
        }
    }

    setupEventListeners() {
        // Listen for page changes
        window.addEventListener('pageLoaded', (e) => {
            this.currentPage = e.detail.page;
            this.updateActiveState();
        });

        // Listen for route changes
        window.addEventListener('popstate', () => {
            this.updateActiveState();
        });
    }

    updateActiveState() {
        const currentPath = window.location.pathname;
        const navLinks = document.querySelectorAll('.nav-links a[data-page]');
        
        navLinks.forEach(link => {
            const page = link.getAttribute('data-page');
            const linkPath = `/${page}`;
            
            if (currentPath === linkPath || (currentPath === '/' && page === 'landing')) {
                link.classList.add('active');
            } else {
                link.classList.remove('active');
            }
        });
    }

    /**
     * Add navigation to current page if it doesn't exist
     */
    static inject() {
        if (!window.navigationComponent) {
            window.navigationComponent = new Navigation();
        }
        return window.navigationComponent;
    }

    /**
     * Update navigation for specific page
     */
    static updateForPage(pageName) {
        const nav = Navigation.inject();
        nav.currentPage = pageName;
        nav.updateActiveState();
    }
}

/**
 * Initialize navigation component
 */
export function initNavigation() {
    return Navigation.inject();
}

// Auto-initialize when module is loaded
document.addEventListener('DOMContentLoaded', () => {
    initNavigation();
});

export default Navigation;
