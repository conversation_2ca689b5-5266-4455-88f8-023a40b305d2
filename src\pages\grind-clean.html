<!-- Grind page content -->
<nav class="top-nav">
    <div class="container">
        <div class="d-flex justify-content-between align-items-center">
            <div class="nav-brand d-flex align-items-center">
                <img src="src/assets/images/gpace-logo-white.png" alt="GPAce Logo" style="height: 60px; margin-right: 10px;">
                <a href="/" onclick="event.preventDefault(); window.navigate('/')" style="text-decoration: none; color: inherit;">GPAce</a>
            </div>
            <div class="nav-links">
                <a href="/grind" onclick="event.preventDefault(); window.navigate('/grind')" data-page="grind" class="active">Grind Mode</a>
                <a href="/instant-test-feedback" onclick="event.preventDefault(); window.navigate('/instant-test-feedback')" data-page="instant-test-feedback">Test Feedback</a>
                <a href="/study-spaces" onclick="event.preventDefault(); window.navigate('/study-spaces')" data-page="study-spaces">Grind Station</a>
                <a href="/daily-calendar" onclick="event.preventDefault(); window.navigate('/daily-calendar')" data-page="daily-calendar">Daily Drip</a>
                <a href="/academic-details" onclick="event.preventDefault(); window.navigate('/academic-details')" data-page="academic-details">Brain Juice</a>
                <a href="/extracted" onclick="event.preventDefault(); window.navigate('/extracted')" data-page="extracted">Hustle Hub</a>
                <a href="/subject-marks" onclick="event.preventDefault(); window.navigate('/subject-marks')" data-page="subject-marks">Subject Marks</a>
                <a href="/flashcards" onclick="event.preventDefault(); window.navigate('/flashcards')" data-page="flashcards">Flashcards</a>
                <button class="drawer-toggle">
                    <i class="bi bi-gear"></i>
                </button>
            </div>
        </div>
    </div>
</nav>

<div class="container-fluid grind-container">
    <div class="row">
        <!-- Left Sidebar -->
        <div class="col-md-3 grind-sidebar">
            <div class="sidebar-section">
                <h5>Current Session</h5>
                <div class="session-timer">
                    <div class="timer-display">25:00</div>
                    <div class="timer-controls">
                        <button class="btn btn-primary btn-sm" id="startTimer">Start</button>
                        <button class="btn btn-secondary btn-sm" id="pauseTimer">Pause</button>
                        <button class="btn btn-outline-secondary btn-sm" id="resetTimer">Reset</button>
                    </div>
                </div>
            </div>
            
            <div class="sidebar-section">
                <h5>Today's Tasks</h5>
                <div class="task-list">
                    <div class="task-item">
                        <input type="checkbox" id="task1">
                        <label for="task1">Complete Math Assignment</label>
                    </div>
                    <div class="task-item">
                        <input type="checkbox" id="task2">
                        <label for="task2">Review Chemistry Notes</label>
                    </div>
                    <div class="task-item">
                        <input type="checkbox" id="task3">
                        <label for="task3">Prepare for History Quiz</label>
                    </div>
                </div>
                <button class="btn btn-outline-primary btn-sm mt-2">Add Task</button>
            </div>
            
            <div class="sidebar-section">
                <h5>Study Stats</h5>
                <div class="stats-grid">
                    <div class="stat-item">
                        <div class="stat-value">4</div>
                        <div class="stat-label">Sessions Today</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">2h 30m</div>
                        <div class="stat-label">Total Time</div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Main Content Area -->
        <div class="col-md-6 grind-main">
            <div class="grind-header">
                <h2>Grind Mode</h2>
                <p>Focus on your studies with AI-powered assistance and smart tools.</p>
            </div>
            
            <div class="grind-workspace">
                <div class="workspace-tabs">
                    <button class="tab-btn active" data-tab="notes">Notes</button>
                    <button class="tab-btn" data-tab="research">AI Research</button>
                    <button class="tab-btn" data-tab="flashcards">Flashcards</button>
                    <button class="tab-btn" data-tab="calculator">Calculator</button>
                </div>
                
                <div class="tab-content">
                    <div class="tab-pane active" id="notes">
                        <div class="notes-editor">
                            <div class="editor-toolbar">
                                <button class="btn btn-sm btn-outline-secondary"><i class="fas fa-bold"></i></button>
                                <button class="btn btn-sm btn-outline-secondary"><i class="fas fa-italic"></i></button>
                                <button class="btn btn-sm btn-outline-secondary"><i class="fas fa-underline"></i></button>
                                <button class="btn btn-sm btn-outline-secondary"><i class="fas fa-list"></i></button>
                            </div>
                            <textarea class="form-control notes-textarea" placeholder="Start taking notes..." rows="15"></textarea>
                        </div>
                    </div>
                    
                    <div class="tab-pane" id="research">
                        <div class="ai-research-panel">
                            <div class="research-input">
                                <input type="text" class="form-control" placeholder="Ask AI anything about your studies...">
                                <button class="btn btn-primary">Research</button>
                            </div>
                            <div class="research-results">
                                <p class="text-muted">AI research results will appear here...</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="tab-pane" id="flashcards">
                        <div class="flashcard-panel">
                            <div class="flashcard">
                                <div class="flashcard-front">
                                    <h5>What is the derivative of x²?</h5>
                                </div>
                                <div class="flashcard-back" style="display: none;">
                                    <h5>2x</h5>
                                </div>
                            </div>
                            <div class="flashcard-controls">
                                <button class="btn btn-secondary">Previous</button>
                                <button class="btn btn-primary">Flip</button>
                                <button class="btn btn-secondary">Next</button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="tab-pane" id="calculator">
                        <div class="calculator-panel">
                            <div class="calculator">
                                <div class="calculator-display">0</div>
                                <div class="calculator-buttons">
                                    <button class="btn btn-outline-secondary">C</button>
                                    <button class="btn btn-outline-secondary">±</button>
                                    <button class="btn btn-outline-secondary">%</button>
                                    <button class="btn btn-warning">÷</button>
                                    <button class="btn btn-outline-secondary">7</button>
                                    <button class="btn btn-outline-secondary">8</button>
                                    <button class="btn btn-outline-secondary">9</button>
                                    <button class="btn btn-warning">×</button>
                                    <button class="btn btn-outline-secondary">4</button>
                                    <button class="btn btn-outline-secondary">5</button>
                                    <button class="btn btn-outline-secondary">6</button>
                                    <button class="btn btn-warning">-</button>
                                    <button class="btn btn-outline-secondary">1</button>
                                    <button class="btn btn-outline-secondary">2</button>
                                    <button class="btn btn-outline-secondary">3</button>
                                    <button class="btn btn-warning">+</button>
                                    <button class="btn btn-outline-secondary" style="grid-column: span 2;">0</button>
                                    <button class="btn btn-outline-secondary">.</button>
                                    <button class="btn btn-primary">=</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Right Sidebar -->
        <div class="col-md-3 grind-sidebar-right">
            <div class="sidebar-section">
                <h5>Quick Actions</h5>
                <div class="quick-actions">
                    <button class="btn btn-outline-primary btn-sm w-100 mb-2">
                        <i class="fas fa-microphone"></i> Voice Notes
                    </button>
                    <button class="btn btn-outline-primary btn-sm w-100 mb-2">
                        <i class="fas fa-camera"></i> Scan Document
                    </button>
                    <button class="btn btn-outline-primary btn-sm w-100 mb-2">
                        <i class="fas fa-share"></i> Share Session
                    </button>
                </div>
            </div>
            
            <div class="sidebar-section">
                <h5>Focus Music</h5>
                <div class="music-player">
                    <div class="music-track">
                        <div class="track-info">
                            <div class="track-title">Lo-fi Study Beats</div>
                            <div class="track-artist">Focus Playlist</div>
                        </div>
                        <div class="music-controls">
                            <button class="btn btn-sm btn-outline-secondary">
                                <i class="fas fa-play"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="sidebar-section">
                <h5>Study Environment</h5>
                <div class="environment-controls">
                    <div class="control-item">
                        <label>Background Noise</label>
                        <select class="form-select form-select-sm">
                            <option>None</option>
                            <option>Rain</option>
                            <option>Coffee Shop</option>
                            <option>Forest</option>
                        </select>
                    </div>
                    <div class="control-item">
                        <label>Theme</label>
                        <select class="form-select form-select-sm">
                            <option>Dark</option>
                            <option>Light</option>
                            <option>Auto</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.grind-container {
    min-height: 100vh;
    background-color: #121212;
    color: #ffffff;
    padding-top: 20px;
}

.grind-sidebar, .grind-sidebar-right {
    background-color: #1e1e1e;
    padding: 20px;
    border-radius: 10px;
    margin: 10px;
}

.grind-main {
    padding: 20px;
}

.sidebar-section {
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid #333;
}

.sidebar-section:last-child {
    border-bottom: none;
}

.session-timer {
    text-align: center;
}

.timer-display {
    font-size: 2rem;
    font-weight: bold;
    color: #fe2c55;
    margin-bottom: 15px;
}

.timer-controls {
    display: flex;
    gap: 10px;
    justify-content: center;
}

.task-item {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
}

.task-item input[type="checkbox"] {
    margin-right: 10px;
}

.stats-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
}

.stat-item {
    text-align: center;
    padding: 10px;
    background-color: rgba(255,255,255,0.05);
    border-radius: 8px;
}

.stat-value {
    font-size: 1.2rem;
    font-weight: bold;
    color: #25f4ee;
}

.stat-label {
    font-size: 0.8rem;
    color: rgba(255,255,255,0.7);
}

.grind-workspace {
    background-color: #1e1e1e;
    border-radius: 10px;
    padding: 20px;
}

.workspace-tabs {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
}

.tab-btn {
    background: none;
    border: 1px solid #333;
    color: rgba(255,255,255,0.7);
    padding: 10px 20px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.tab-btn.active,
.tab-btn:hover {
    background-color: #fe2c55;
    color: white;
    border-color: #fe2c55;
}

.tab-pane {
    display: none;
}

.tab-pane.active {
    display: block;
}

.notes-textarea {
    background-color: #2d2d2d;
    border: 1px solid #333;
    color: #ffffff;
    resize: vertical;
}

.notes-textarea:focus {
    background-color: #2d2d2d;
    border-color: #fe2c55;
    color: #ffffff;
    box-shadow: 0 0 0 0.2rem rgba(254, 44, 85, 0.25);
}

.editor-toolbar {
    margin-bottom: 10px;
    display: flex;
    gap: 5px;
}

.research-input {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
}

.research-results {
    background-color: #2d2d2d;
    border-radius: 8px;
    padding: 20px;
    min-height: 200px;
}

.flashcard {
    background-color: #2d2d2d;
    border-radius: 10px;
    padding: 40px;
    text-align: center;
    margin-bottom: 20px;
    min-height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.flashcard-controls {
    display: flex;
    justify-content: center;
    gap: 10px;
}

.calculator {
    max-width: 300px;
    margin: 0 auto;
}

.calculator-display {
    background-color: #2d2d2d;
    border: 1px solid #333;
    border-radius: 8px;
    padding: 20px;
    text-align: right;
    font-size: 1.5rem;
    margin-bottom: 15px;
}

.calculator-buttons {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 10px;
}

.music-track {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    background-color: rgba(255,255,255,0.05);
    border-radius: 8px;
}

.track-title {
    font-weight: 500;
}

.track-artist {
    font-size: 0.8rem;
    color: rgba(255,255,255,0.7);
}

.control-item {
    margin-bottom: 15px;
}

.control-item label {
    display: block;
    margin-bottom: 5px;
    font-size: 0.9rem;
    color: rgba(255,255,255,0.8);
}

.form-select {
    background-color: #2d2d2d;
    border: 1px solid #333;
    color: #ffffff;
}

.form-select:focus {
    background-color: #2d2d2d;
    border-color: #fe2c55;
    color: #ffffff;
    box-shadow: 0 0 0 0.2rem rgba(254, 44, 85, 0.25);
}
</style>
