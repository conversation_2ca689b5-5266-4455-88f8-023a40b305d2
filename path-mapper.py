#!/usr/bin/env python3
"""
Advanced Path Mapping and Reference Updater
Handles complex path transformations and maintains reference integrity
"""

import os
import re
import json
from pathlib import Path
from typing import Dict, List, Tuple, Set
import argparse

class PathMapper:
    def __init__(self, root_dir: str = "."):
        self.root = Path(root_dir).resolve()
        self.mappings = {}
        self.reverse_mappings = {}
        self.updated_files = set()
        
    def add_mapping(self, old_path: str, new_path: str):
        """Add a path mapping"""
        self.mappings[old_path] = new_path
        self.reverse_mappings[new_path] = old_path
        
    def load_mappings_from_file(self, mapping_file: str):
        """Load path mappings from JSON file"""
        with open(mapping_file, 'r') as f:
            mappings = json.load(f)
            for old_path, new_path in mappings.items():
                self.add_mapping(old_path, new_path)
                
    def save_mappings_to_file(self, mapping_file: str):
        """Save current mappings to JSON file"""
        with open(mapping_file, 'w') as f:
            json.dump(self.mappings, f, indent=2)
            
    def detect_moved_files(self) -> Dict[str, str]:
        """Auto-detect moved files by comparing old and new structures"""
        detected_mappings = {}
        
        # Common reorganization patterns
        patterns = [
            (r'^assets/', 'src/assets/'),
            (r'^css/', 'src/css/'),
            (r'^js/', 'src/js/'),
            (r'^data/', 'src/data/'),
            (r'^sounds/', 'src/assets/audio/sounds/'),
            (r'^alarm-sounds/', 'src/assets/audio/alarms/'),
            (r'^relaxed-mode/index\.html$', 'src/pages/relaxed-mode.html'),
        ]
        
        for old_pattern, new_pattern in patterns:
            detected_mappings[old_pattern] = new_pattern
            
        return detected_mappings
    
    def find_references_to_path(self, target_path: str) -> List[Tuple[str, int, str]]:
        """Find all references to a specific path"""
        references = []
        
        # Search in HTML, CSS, JS files
        file_patterns = ['*.html', '*.css', '*.js', '*.json']
        
        for pattern in file_patterns:
            for file_path in self.root.rglob(pattern):
                if file_path.is_file():
                    try:
                        content = file_path.read_text(encoding='utf-8')
                        lines = content.split('\n')
                        
                        for line_num, line in enumerate(lines, 1):
                            if target_path in line:
                                references.append((str(file_path), line_num, line.strip()))
                                
                    except Exception as e:
                        print(f"Error reading {file_path}: {e}")
                        
        return references
    
    def update_references_in_file(self, file_path: Path, mappings: Dict[str, str]) -> bool:
        """Update path references in a single file"""
        try:
            content = file_path.read_text(encoding='utf-8')
            original_content = content
            
            # Apply mappings
            for old_path, new_path in mappings.items():
                # Handle different quote styles and contexts
                patterns = [
                    (f'"{old_path}', f'"{new_path}'),
                    (f"'{old_path}", f"'{new_path}"),
                    (f'({old_path}', f'({new_path}'),
                    (f' {old_path}', f' {new_path}'),
                ]
                
                for old_pattern, new_pattern in patterns:
                    content = content.replace(old_pattern, new_pattern)
                    
                # Handle regex patterns for more complex cases
                if old_path.endswith('/'):
                    # Directory references
                    regex_pattern = re.compile(
                        rf'(["\']){re.escape(old_path)}([^"\']*?)(["\'])',
                        re.IGNORECASE
                    )
                    content = regex_pattern.sub(
                        rf'\1{new_path}\2\3',
                        content
                    )
            
            # Only write if content changed
            if content != original_content:
                file_path.write_text(content, encoding='utf-8')
                self.updated_files.add(str(file_path))
                return True
                
        except Exception as e:
            print(f"Error updating {file_path}: {e}")
            
        return False
    
    def update_all_references(self, mappings: Dict[str, str] = None):
        """Update all references using current mappings"""
        if mappings is None:
            mappings = self.mappings
            
        print(f"Updating references with {len(mappings)} mappings...")
        
        # File types to update
        file_patterns = ['*.html', '*.css', '*.js', '*.json']
        
        for pattern in file_patterns:
            for file_path in self.root.rglob(pattern):
                if (file_path.is_file() and 
                    not any(skip in str(file_path) for skip in ['.git', 'node_modules', '.backup'])):
                    
                    if self.update_references_in_file(file_path, mappings):
                        print(f"Updated: {file_path}")
                        
        print(f"Updated {len(self.updated_files)} files")
    
    def validate_mappings(self) -> List[str]:
        """Validate that all mapped paths exist"""
        issues = []
        
        for old_path, new_path in self.mappings.items():
            new_full_path = self.root / new_path
            if not new_full_path.exists():
                issues.append(f"Target path does not exist: {new_path}")
                
        return issues
    
    def generate_mapping_report(self) -> str:
        """Generate a report of all mappings and their status"""
        report = []
        report.append("PATH MAPPING REPORT")
        report.append("=" * 50)
        
        for old_path, new_path in self.mappings.items():
            new_full_path = self.root / new_path
            status = "✅ EXISTS" if new_full_path.exists() else "❌ MISSING"
            report.append(f"{old_path} → {new_path} {status}")
            
        report.append(f"\nFiles updated: {len(self.updated_files)}")
        report.append("\nUpdated files:")
        for file_path in sorted(self.updated_files):
            report.append(f"  - {file_path}")
            
        return "\n".join(report)
    
    def create_reverse_mappings(self):
        """Create mappings to undo changes"""
        reverse_file = self.root / "reverse-mappings.json"
        with open(reverse_file, 'w') as f:
            json.dump(self.reverse_mappings, f, indent=2)
        print(f"Reverse mappings saved to: {reverse_file}")
    
    def smart_path_resolution(self, reference: str, from_file: Path) -> str:
        """Intelligently resolve relative paths"""
        if reference.startswith(('http://', 'https://', 'data:', '#')):
            return reference
            
        # Calculate relative path from current file to new location
        if reference.startswith('/'):
            # Absolute path
            return reference
        else:
            # Relative path - need to recalculate
            current_dir = from_file.parent
            target = current_dir / reference
            
            # Check if this path exists in our mappings
            for old_path, new_path in self.mappings.items():
                if str(target).endswith(old_path):
                    # Calculate new relative path
                    new_target = self.root / new_path
                    try:
                        relative_path = os.path.relpath(new_target, current_dir)
                        return relative_path.replace('\\', '/')
                    except ValueError:
                        return new_path
                        
        return reference

def main():
    parser = argparse.ArgumentParser(description="Advanced path mapping and reference updater")
    parser.add_argument("--root", default=".", help="Root directory")
    parser.add_argument("--mappings", help="JSON file with path mappings")
    parser.add_argument("--auto-detect", action="store_true", help="Auto-detect moved files")
    parser.add_argument("--update", action="store_true", help="Update all references")
    parser.add_argument("--validate", action="store_true", help="Validate mappings")
    parser.add_argument("--report", action="store_true", help="Generate mapping report")
    parser.add_argument("--create-reverse", action="store_true", help="Create reverse mappings")
    
    args = parser.parse_args()
    
    mapper = PathMapper(args.root)
    
    # Load mappings
    if args.mappings and os.path.exists(args.mappings):
        mapper.load_mappings_from_file(args.mappings)
        print(f"Loaded mappings from: {args.mappings}")
    
    # Auto-detect moved files
    if args.auto_detect:
        detected = mapper.detect_moved_files()
        for old_path, new_path in detected.items():
            mapper.add_mapping(old_path, new_path)
        print(f"Auto-detected {len(detected)} mappings")
    
    # Validate mappings
    if args.validate:
        issues = mapper.validate_mappings()
        if issues:
            print("Validation issues:")
            for issue in issues:
                print(f"  ❌ {issue}")
        else:
            print("✅ All mappings valid")
    
    # Update references
    if args.update:
        mapper.update_all_references()
    
    # Generate report
    if args.report:
        report = mapper.generate_mapping_report()
        print(report)
        
        # Save report to file
        report_file = Path(args.root) / "path-mapping-report.txt"
        report_file.write_text(report)
        print(f"\nReport saved to: {report_file}")
    
    # Create reverse mappings
    if args.create_reverse:
        mapper.create_reverse_mappings()
    
    # Save current mappings
    if mapper.mappings:
        mapping_file = Path(args.root) / "current-mappings.json"
        mapper.save_mappings_to_file(mapping_file)
        print(f"Current mappings saved to: {mapping_file}")

if __name__ == "__main__":
    main()
