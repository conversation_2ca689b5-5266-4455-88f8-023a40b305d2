



  
      <button onclick="window.location.href='src/pages/index.html'" class="relaxed-mode-btn">
        <i class="bi bi-cloud-sun"></i>
        Relaxed Mode
      </button>
      <button class="workspace-toggle" id="workspaceToggle">
          <i class="fas fa-code"></i>
        </button>

        <div class="workspace-overlay" id="workspaceOverlay"></div>

        <div class="workspace-panel" id="workspacePanel">
          <div class="workspace-header">
            <h2>Workspace</h2>
            <button class="workspace-close" id="workspaceClose">&times;</button>
          </div>
          <div class="workspace-content">
            <iframe src="workspace.html" frameborder="0" style="width: 100%; height: 100%;"></iframe>
          </div>
        </div>

        <!-- Add Link Modal -->
<div id="addLinkModal" class="modal-overlay" style="display: none;">
  <div class="add-link-modal">
      <h3>Add New Link</h3>
      <form class="add-link-form" id="addLinkForm">
          <div class="form-group">
              <label for="linkUrl">URL</label>
              <input type="url" id="linkUrl" required placeholder="https://...">
          </div>
          <div class="form-group">
              <label for="linkTitle">Title</label>
              <input type="text" id="linkTitle" required placeholder="Enter link title">
          </div>
          <div class="form-group">
              <label for="linkDescription">Description (optional)</label>
              <textarea id="linkDescription" rows="3" placeholder="Enter link description"></textarea>
          </div>
          <div class="modal-actions">
              <button type="button" class="modal-btn cancel" onclick="closeAddLinkModal()">Cancel</button>
              <button type="submit" class="modal-btn save">Save Link</button>
          </div>
      </form>
  </div>
</div>

        <div class="priority-task-container">
          <div class="task-header">
            <h3 class="task-title"></h3>
            <span class="task-project"></span>
          </div>
        </div>

        <!-- Add Subject Material Modal -->
        <div class="modal fade" id="addSubjectMaterialModal" tabindex="-1" aria-labelledby="addSubjectMaterialModalLabel" aria-hidden="true">
          <div class="modal-dialog">
            <div class="modal-content">
              <div class="modal-header">
                <h5 class="modal-title" id="addSubjectMaterialModalLabel">Add Subject Material</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close" onclick="console.log('Modal closed')"></button>
              </div>
              <div class="modal-body">
                <div class="mb-3">
                  <label for="subjectSelect" class="form-label">Subject</label>
                  <select class="form-select" id="subjectSelect" onchange="console.log('Subject selected:', this.value)">
                    <option value="">Select a subject...</option>
                  </select>
                </div>
                <div class="mb-3">
                  <label for="materialType" class="form-label">Material Type</label>
                  <select class="form-select" id="materialType" onchange="console.log('Material type selected:', this.value)">
                    <option value="textbook">Textbook</option>
                    <option value="notes">Course Notes</option>
                    <option value="syllabus">Syllabus</option>
                    <option value="slides">Lecture Slides</option>
                    <option value="reference">Reference Material</option>
                    <option value="other">Other</option>
                  </select>
                </div>
                <div class="mb-3">
                  <label for="subjectMaterialFile" class="form-label">Choose File</label>
                  <input type="file" class="form-control" id="subjectMaterialFile" onchange="console.log('File selected:', this.files[0])">
                  <div class="form-text">Upload files related to this subject (PDF, Word, PowerPoint, etc.)</div>
                </div>
                <!-- Add progress bar container -->
                <div class="upload-progress d-none">
                  <div class="progress mb-2">
                    <div class="progress-bar" role="progressbar" style="width: 0%" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100"></div>
                  </div>
                  <div class="upload-status small text-muted">
                    <span class="upload-percentage">0%</span> -
                    <span class="upload-speed">0 KB/s</span> -
                    <span class="upload-remaining">Calculating...</span>
                  </div>
                </div>
              </div>
              <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal" onclick="console.log('Upload cancelled')">Cancel</button>
                <button type="button" class="btn btn-primary" id="uploadSubjectMaterial" onclick="handleSubjectMaterialUpload()">Upload</button>
              </div>
            </div>
          </div>
        </div>

        <nav class="top-nav">
          <div class="nav-brand d-flex align-items-center">
            <img src="src/assets/images/gpace-logo-white.png" alt="GPAce Logo" style="height: 60px; margin-right: 0px;">
            <a href="grind.html" style="text-decoration: none; color: inherit;">GPAce</a>
          </div>
          <div class="nav-links">
            <a href="grind.html" class="active">Grind Mode</a>
            <a href="instant-test-feedback.html">Test Feedback</a>
            <a href="study-spaces.html">Grind Station</a>
            <a href="daily-calendar.html">Daily Drip</a>
            <a href="academic-details.html">Brain Juice</a>
            <a href="extracted.html">Hustle Hub</a>
            <a href="subject-marks.html">Subject Marks</a>
            <a href="flashcards.html">Flashcards</a>
          </div>
        </nav>

        <div class="container">
          <div class="priority-task-box" id="priorityTaskBox">
            <!-- Priority task will be displayed here -->
          </div>

          <div id="currentTaskDisplay" class="current-task-header">
            <h1 id="taskTitle">No Current Task</h1>
          </div>

          <!-- Motivational Quote Section -->
          <div class="quote-container">
            <div id="dynamicQuoteContainer">
              <img class="quote-image" src="" alt="Quote Image" style="display: none;">
              <div class="quote-content">
                <p class="quote-text"></p>
                <p class="quote-author"></p>
              </div>
            </div>
            <div class="quote-controls">
              <button onclick="rotateQuote(-1)" class="quote-nav-btn">
                <i class="bi bi-chevron-left"></i>
              </button>
              <button onclick="rotateQuote(1)" class="quote-nav-btn">
                <i class="bi bi-chevron-right"></i>
              </button>
            </div>
          </div>




          <div class="task-container">
            <div class="pomodoro-container">
              <div class="timer-mode-selector modern">
                <button class="timer-mode-btn modern active" data-time="25" data-mode="focus">
                  <i class="fas fa-brain"></i> Focus
                </button>
                <button class="timer-mode-btn modern" data-time="5" data-mode="break">
                  <i class="fas fa-coffee"></i> Break
                </button>
                <div class="custom-time-input modern">
                  <input type="number" id="customTimeInput" min="1" max="60" value="25">
                  <span>min</span>
                </div>
              </div>

              <div class="timer-display-container modern">
                <div class="timer-circle modern">
                  <div class="timer-progress modern"></div>
                  <div class="timer-text">
                    <div class="timer-time modern" id="timer">25:00</div>
                    <div class="timer-label modern">Focus Time</div>
                  </div>
                </div>
              </div>

              <div class="timer-controls modern">
                <button class="timer-btn modern primary" id="startBtn" data-state="paused">
                  <i class="fas fa-play"></i>
                </button>
                <button class="timer-btn modern" id="resetBtn" data-state="reset">
                  <i class="fas fa-redo-alt"></i>
                </button>
              </div>

              <div class="timer-stats modern">
                <div class="stat-item modern">
                  <i class="fas fa-check-circle"></i>
                  <span id="pomodoroCount">0</span>
                </div>
                <div class="stat-item modern">
                  <i class="fas fa-clock"></i>
                  <span id="currentTime"></span>
                </div>
              </div>
            </div>
            <div class="stats-container modern">
              <div class="progress-stats">
                <div class="stat-row modern-stats">
                  <div class="stat-card modern">
                    <div class="stat-icon"><i class="fas fa-clock"></i></div>
                    <div class="stat-content">
                      <div class="stat-value" id="totalWorkTime">00:00:00</div>
                      <div class="stat-label">Time Worked</div>
                    </div>
                  </div>
                  <div class="stat-card modern">
                    <div class="stat-icon"><i class="fas fa-moon"></i></div>
                    <div class="stat-content">
                      <div class="stat-value" id="sleepTimeDisplay">Not Set</div>
                      <div class="stat-label">Time Until Sleep</div>
                    </div>
                  </div>
                  <div class="stat-card modern">
                    <div class="stat-icon"><i class="fas fa-chart-pie"></i></div>
                    <div class="stat-content">
                      <div class="stat-value" id="timeUtilization">0%</div>
                      <div class="stat-label">Time Utilization</div>
                    </div>
                  </div>
                </div>
                <div class="hologram-container" id="hologramContainer">
                  <div class="hologram-overlay"></div>
                </div>
                <div class="energy-graph-container">
                  <h3>Today's Energy Levels</h3>
                  <canvas id="energyGraph"></canvas>
                </div>
              </div>
            </div>

          </div>
        </div>
        <div class="notification" id="notification">
          Session Complete!
        </div>

        <button class="add-task-button" onclick="showTaskModal()">
          <i class="bi bi-plus-lg"></i>
        </button>

        <a href="https://forms.gle/LnMspth72dFMeVV17" target="_blank" class="feedback-button">
          <i class="fas fa-comment"></i>
          <span>Feedback</span>
        </a>

        <!-- Task Creation Modal -->
        <div id="taskModal" class="task-modal">
          <div class="task-modal-content">
            <span class="modal-close" onclick="hideTaskModal()">
              <i class="fas fa-times"></i>
            </span>
            <h2>Add New Task</h2>
            <div class="form-group">
              <label for="projectSelect">Project</label>
              <select id="projectSelect" onchange="loadSubcategories()"></select>
            </div>
            <div class="form-group">
              <label for="subcategorySelect">Subcategory</label>
              <select id="subcategorySelect"></select>
            </div>
            <div class="form-group">
              <label for="taskTitleInput">Task Title</label>
              <input type="text" id="taskTitleInput" required>
            </div>
            <div class="form-group">
              <label for="taskDescription">Description</label>
              <textarea id="taskDescription" rows="3"></textarea>
            </div>
            <div class="form-group">
              <label for="taskDueDate">Due Date</label>
              <input type="datetime-local" id="taskDueDate">
            </div>
            <div class="form-group">
              <label for="taskPriority">Priority</label>
              <select id="taskPriority">
                <option value="low">Low</option>
                <option value="medium">Medium</option>
                <option value="high">High</option>
              </select>
            </div>
            <!-- File Attachment Section -->
            <div class="form-group">
              <label>Attachments</label>
              <div class="file-upload-area" id="modalFileUploadArea">
                <i class="fas fa-cloud-upload-alt"></i>
                <div>Drag and drop files here</div>
                <div class="file-upload-hint">or click to select files</div>
                <input type="file" id="modalFileUploadInput" multiple style="display: none">
              </div>
              <div class="upload-preview mt-2" id="modalUploadPreview"></div>
            </div>
            <button class="create-task-btn" onclick="createTask()">Create Task</button>
          </div>
        </div>

        <!-- Fatigue Level Modal -->
        <div class="fatigue-modal" id="fatigueModal">
          <div class="fatigue-modal-content">
            <h2>How's your energy level?</h2>
            <p>Select your current energy state to start the session</p>

            <div class="fatigue-levels">
              <div class="fatigue-level" data-level="1">
                <i class="fas fa-bolt"></i>
                <div>
                  <h3>1. Fully Alert</h3>
                  <p>Peak energy, wide awake</p>
                </div>
              </div>
              <div class="fatigue-level" data-level="2">
                <i class="fas fa-sun"></i>
                <div>
                  <h3>2. Very Lively</h3>
                  <p>High energy, responsive</p>
                </div>
              </div>
              <div class="fatigue-level" data-level="3">
                <i class="fas fa-smile"></i>
                <div>
                  <h3>3. Okay, Fresh</h3>
                  <p>Good energy, clear-minded</p>
                </div>
              </div>
              <div class="fatigue-level" data-level="4">
                <i class="fas fa-meh"></i>
                <div>
                  <h3>4. A Little Tired</h3>
                  <p>Mild fatigue, functional</p>
                </div>
              </div>
              <div class="fatigue-level" data-level="5">
                <i class="fas fa-frown"></i>
                <div>
                  <h3>5. Moderately Tired</h3>
                  <p>Noticeable fatigue</p>
                </div>
              </div>
              <div class="fatigue-level" data-level="6">
                <i class="fas fa-tired"></i>
                <div>
                  <h3>6. Extremely Tired</h3>
                  <p>Struggling to focus</p>
                </div>
              </div>
              <div class="fatigue-level" data-level="7">
                <i class="fas fa-bed"></i>
                <div>
                  <h3>7. Completely Exhausted</h3>
                  <p>Unable to function well</p>
                </div>
              </div>
            </div>

            <div class="fatigue-modal-buttons">
              <button class="modal-btn" id="cancelFatigue">
                <i class="fas fa-times"></i> Skip
              </button>
              <button class="modal-btn primary" id="confirmFatigue" disabled>
                <i class="fas fa-check"></i> Start
              </button>
            </div>
          </div>
        </div>











          <!-- AI Container Toggle Button (Outside container for better visibility) -->
          <button id="aiContainerToggle" class="ai-container-toggle" title="Hide/Show AI Container (Alt+A)">
            <i class="fas fa-chevron-down fa-lg"></i>
          </button>

          <!-- AI Researcher Section -->
          <div class="ai-researcher-container modern">
            <div class="research-card modern">
              <!-- API Configuration -->
              <div class="api-config modern" id="apiConfigSection" style="display: none;">
                <div class="api-config-header">
                  <h3>API Configuration</h3>
                  <button class="close-btn" onclick="toggleApiConfig()">
                    <i class="fas fa-times"></i>
                  </button>
                </div>
                <div class="form-group modern">
                  <label for="geminiApiKey">Google Gemini API Key</label>
                  <div class="input-group modern">
                    <input type="password" class="form-control modern" id="geminiApiKey" placeholder="Enter Gemini API Key">
                    <button type="button" class="eye-btn" onclick="toggleApiVisibility('geminiApiKey')">
                      <i class="bi bi-eye"></i>
                    </button>
                  </div>
                </div>

                <div class="form-group modern">
                  <label for="geminiModel">Gemini Model Selection</label>
                  <select class="form-control modern" id="geminiModel">
                    <option value="gemini-2.5-pro-preview-03-25">Gemini 2.5 Pro Preview 03-25 (NEW)</option>
                    <option value="gemini-2.5-flash-preview-04-17">Gemini 2.5 Flash Preview 04-17 (NEW)</option>
                    <option value="gemini-2.5-pro-exp-03-25">Gemini 2.5 Pro Experimental (Best quality)</option>
                    <option value="gemini-2.0-flash" selected>Gemini 2.0 Flash (Balanced)</option>
                    <option value="gemini-1.5-pro">Gemini 1.5 Pro (Legacy)</option>
                  </select>
                  <small class="form-text text-muted">Select the model to use for both content research and simulation generation. Gemini 2.5 Pro offers the highest quality results but may be slower.</small>
                </div>

                <div class="form-group modern">
                  <label for="geminiTemperature">Response Creativity</label>
                  <div class="d-flex align-items-center">
                    <span class="me-2 small">Precise</span>
                    <input type="range" class="form-range flex-grow-1" id="geminiTemperature" min="0" max="1" step="0.1" value="0.4">
                    <span class="ms-2 small">Creative</span>
                  </div>
                  <div class="text-center mt-1">
                    <span id="temperatureDisplay" class="badge bg-primary">Balanced (0.4)</span>
                  </div>
                  <small class="form-text text-muted">Adjust how creative or precise the AI responses will be. Lower values produce more predictable responses, higher values produce more varied and creative ones.</small>
                </div>

                <div class="form-group modern">
                  <label for="wolframAlphaApiKey">Wolfram Alpha API Key</label>
                  <div class="input-group modern">
                    <input type="password" class="form-control modern" id="wolframAlphaApiKey" placeholder="Enter Wolfram Alpha API Key">
                    <button type="button" class="eye-btn" onclick="toggleApiVisibility('wolframAlphaApiKey')">
                      <i class="bi bi-eye"></i>
                    </button>
                  </div>
                </div>
                <div class="form-group modern">
                  <label for="tavilyApiKey">Tavily API Key</label>
                  <div class="input-group modern">
                    <input type="password" class="form-control modern" id="tavilyApiKey" placeholder="Enter Tavily API Key">
                    <button type="button" class="eye-btn" onclick="toggleApiVisibility('tavilyApiKey')">
                      <i class="bi bi-eye"></i>
                    </button>
                  </div>
                </div>
                <button class="save-btn modern" onclick="saveApiKeys()">Save API Keys</button>
              </div>

              <!-- Search Interface -->
              <div class="search-interface modern">
                <div class="search-container">
                  <div class="drop-zone-container modern">
                    <div class="drop-zone modern">
                      <textarea id="searchQuery" rows="1" placeholder="How can I help you?"></textarea>
                      <div class="drop-zone-text">Drop image or PDF here</div>
                    </div>
                    <div class="search-actions">
                      <div class="search-tools">
                        <label for="imageUpload" class="tool-btn" title="Upload an image for analysis">
                          <i class="fas fa-image"></i>
                        </label>
                        <input type="file" id="imageUpload" accept="image/*,application/pdf" style="display: none;">
                        <label for="pdfUpload" class="tool-btn" title="Upload a PDF for analysis">
                          <i class="fas fa-file-pdf"></i>
                        </label>
                        <input type="file" id="pdfUpload" accept="application/pdf" style="display: none;">
                        <button id="toggleApiConfig" class="tool-btn" title="Configure API Keys">
                          <i class="fas fa-cog"></i>
                        </button>
                        <button id="resultsToggleBtn" class="tool-btn" title="Expand AI Container to Full Screen" style="display: none;">
                          <i class="fas fa-expand-alt"></i>
                        </button>
                        <button onclick="performAISearch()" class="search-btn">
                          <i class="fas fa-search"></i>
                        </button>
                      </div>
                      <div class="file-info" id="fileInfo" style="display: none;">
                        <span id="selectedFileName" class="file-name"></span>
                        <button class="clear-btn" id="clearImage" style="display: none;">
                          <i class="fas fa-times"></i>
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Results Area -->
              <div class="results-area modern" style="display: none;">
                <div class="results-actions">
                  <button id="copyResultsBtn" class="action-btn" title="Copy results to clipboard">
                    <i class="fas fa-copy"></i>
                  </button>
                  <button id="downloadResultsPdfBtn" class="action-btn" title="Download results as PDF">
                    <i class="fas fa-file-pdf"></i>
                  </button>
                  <div class="tts-controls">
                    <button id="speakResultsBtn" class="action-btn" title="Read results aloud">
                      <i class="fas fa-volume-up"></i>
                    </button>
                    <button id="pauseResumeTextToSpeechBtn" class="action-btn" title="Pause/Resume reading" disabled>
                      <i class="fas fa-pause"></i>
                    </button>
                    <button id="stopTextToSpeechBtn" class="action-btn" title="Stop reading" disabled>
                      <i class="fas fa-stop"></i>
                    </button>
                    <div class="speed-control-container">
                      <button id="decreaseSpeedBtn" class="action-btn speed-btn" title="Decrease speed" disabled>
                        <i class="fas fa-minus"></i>
                      </button>
                      <div id="currentSpeed" class="current-speed" title="Current playback speed">1x</div>
                      <button id="increaseSpeedBtn" class="action-btn speed-btn" title="Increase speed" disabled>
                        <i class="fas fa-plus"></i>
                      </button>
                    </div>
                    <div class="voice-selector-container">
                      <button id="voiceSettingsBtn" class="action-btn" title="Voice settings">
                        <i class="fas fa-cog"></i>
                      </button>
                      <div id="voiceSettingsDropdown" class="voice-settings-dropdown">
                        <div class="dropdown-header">Voice Settings</div>
                        <div class="dropdown-item">
                          <label for="voiceSelector">Voice:</label>
                          <select id="voiceSelector" class="voice-selector"></select>
                        </div>
                        <div class="dropdown-item">
                          <label for="rateSelector">Speed:</label>
                          <select id="rateSelector" class="rate-selector">
                            <option value="0.5">Very Slow</option>
                            <option value="0.75">Slow</option>
                            <option value="1" selected>Normal</option>
                            <option value="1.25">Fast</option>
                            <option value="1.5">Very Fast</option>
                            <option value="2">2x</option>
                            <option value="2.5">2.5x</option>
                            <option value="3">3x</option>
                          </select>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div id="searchResults" class="research-response modern">
                  <!-- Results will be displayed here -->
                </div>
                <div class="simulation-controls">
                  <button id="generateSimulationBtn" class="simulation-btn" title="Generate an interactive simulation">
                    <i class="fas fa-cube"></i> Generate Simulation
                  </button>
                </div>
                <div id="simulationContainer" class="simulation-container" style="display: none;">
                  <div class="simulation-header">
                    <div class="simulation-title">
                      <h3>Interactive Simulation</h3>
                      <div class="simulation-indicators">
                        <div id="simulationImageIndicator" class="simulation-image-indicator" style="display: none;">
                          <i class="fas fa-image"></i>
                          <span>Image-based</span>
                        </div>
                        <div id="simulationModelIndicator" class="simulation-model-indicator">
                          <i class="fas fa-microchip"></i>
                          <span id="simulationModelName">Gemini 2.0</span>
                        </div>
                      </div>
                    </div>
                    <div class="simulation-actions">
                      <button id="popoutSimulation" class="action-btn" title="Pop out simulation into draggable window">
                        <i class="fas fa-external-link-alt"></i>
                      </button>
                      <button id="reloadSimulation" class="action-btn" title="Regenerate simulation">
                        <i class="fas fa-redo-alt"></i>
                      </button>
                      <button id="downloadSimulation" class="action-btn" title="Download simulation as HTML file">
                        <i class="fas fa-download"></i>
                      </button>
                      <button id="copySimulationCode" class="action-btn" title="Copy simulation code">
                        <i class="fas fa-copy"></i>
                      </button>
                      <button id="closeSimulation" class="action-btn" title="Close simulation">
                        <i class="fas fa-times"></i>
                      </button>
                    </div>
                  </div>
                  <div id="simulationProgress" class="simulation-progress" style="display: none;">
                    <div class="progress-container">
                      <div class="progress-bar" id="simulationProgressBar"></div>
                    </div>
                    <div class="progress-info">
                      <span id="simulationProgressPercent">0%</span>
                      <span id="simulationProgressTime">Estimating time...</span>
                      <span id="simulationProgressTokens">0 tokens</span>
                    </div>
                  </div>
                  <div id="simulationCode" class="simulation-code"></div>
                  <div id="simulationPreview" class="simulation-preview">
                    <div id="simulationReadyMessage" class="simulation-ready-message" style="display: none;">
                      <div class="ready-icon">
                        <i class="fas fa-check-circle"></i>
                      </div>
                      <h3>Simulation Ready!</h3>
                      <p>Your interactive simulation has been generated and is ready to run.</p>
                      <button id="runSimulationBtn" class="run-simulation-btn">
                        <i class="fas fa-play"></i> Run Simulation
                      </button>
                    </div>
                    <iframe id="simulationFrame" sandbox="allow-scripts allow-same-origin allow-forms allow-popups allow-modals allow-pointer-lock" frameborder="0" style="display: none;"></iframe>
                  </div>
                </div>
                <!-- Old toggle button removed -->
              </div>
            </div>
          </div>

<!-- HEREE STARTS THE SCRIPTS -->
  <!-- PDF.js library for PDF processing -->





















<!-- Add this script to provide global firebase namespace for legacy code -->




















<!-- Add this before loading taskLinks.js -->




















































<!-- Pop-out Simulation Window -->
<div id="simulationPopout" class="simulation-popout">
  <div class="simulation-popout-header" id="simulationPopoutHeader">
    <h4 class="simulation-popout-title">Interactive Simulation</h4>
    <div style="display: flex; align-items: center;">
      <label class="aspect-ratio-lock">
        <input type="checkbox" id="lockAspectRatio" checked>
        Lock aspect ratio
      </label>
      <div class="simulation-popout-actions">
        <button id="downloadSimulationPopout" title="Download simulation as HTML file">
          <i class="fas fa-download"></i>
        </button>
        <button id="minimizeSimulationPopout" title="Return to original container">
          <i class="fas fa-compress-alt"></i>
        </button>
        <button id="closeSimulationPopout" title="Close simulation">
          <i class="fas fa-times"></i>
        </button>
      </div>
    </div>
  </div>
  <div class="simulation-popout-content">
    <iframe id="simulationPopoutFrame" sandbox="allow-scripts allow-same-origin allow-forms allow-popups allow-modals allow-pointer-lock" frameborder="0"></iframe>
  </div>

  <!-- Custom resize handles for better usability -->
  <div class="resize-handle corner top-left" id="resizeTopLeft"></div>
  <div class="resize-handle corner top-right" id="resizeTopRight"></div>
  <div class="resize-handle corner bottom-left" id="resizeBottomLeft"></div>
  <div class="resize-handle corner bottom-right" id="resizeBottomRight"></div>
  <div class="resize-handle edge right" id="resizeRight"></div>
  <div class="resize-handle edge bottom" id="resizeBottom"></div>
</div>



<!-- Debug mode toggle script -->


<!-- Timer Title Enforcer Script -->


<!-- Text Expansion Script -->




<!-- Simulation Enhancer Module -->




<!-- Firebase Initialization -->















































































 
