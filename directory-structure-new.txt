gpace/
├─ index.html                           ← Single bootstrapper/router
├─ src/
│  ├─ pages/                           ← Static HTML fragments (16 files → 9 main views)
│  │  ├─ grind.html                    ← 201KB → fragment
│  │  ├─ landing.html                  ← 55KB → fragment  
│  │  ├─ academic-details.html         ← 26KB
│  │  ├─ tasks.html                    ← 24KB
│  │  ├─ workspace.html                ← 21KB
│  │  ├─ study-spaces.html             ← 21KB
│  │  ├─ instant-test-feedback.html    ← 23KB
│  │  ├─ extracted.html                ← 92KB → fragment
│  │  └─ 404.html                      ← 2KB
│  │
│  ├─ components/                      ← Reusable UI pieces (7 components)
│  │  ├─ flashcards/
│  │  │  ├─ flashcard.html             ← from flashcards.html (17KB)
│  │  │  ├─ flashcard.css              ← from css/flashcards.css (5KB)
│  │  │  └─ flashcard.js               ← from js/flashcards.js (52KB)
│  │  ├─ calendar/
│  │  │  ├─ daily-calendar.html        ← from daily-calendar.html (10KB)
│  │  │  ├─ calendar.css               ← from css/daily-calendar.css (50KB)
│  │  │  └─ calendar.js                ← from js/calendar-views.js (33KB)
│  │  ├─ navigation/
│  │  │  ├─ side-drawer.html           ← extract from pages
│  │  │  ├─ side-drawer.css            ← from css/sideDrawer.css (16KB)
│  │  │  └─ side-drawer.js             ← from js/sideDrawer.js (8KB)
│  │  ├─ priority/
│  │  │  ├─ priority-calculator.html   ← from priority-calculator.html (7KB)
│  │  │  ├─ priority-calculator.css    ← from css/priority-calculator.css (8KB)
│  │  │  └─ priority-calculator.js     ← from priority-calculator.js (26KB)
│  │  ├─ settings/
│  │  │  ├─ settings.html              ← from settings.html (6KB)
│  │  │  ├─ settings.css               ← from css/settings.css (7KB)
│  │  │  └─ settings.js                ← combine settings-related JS
│  │  ├─ subject-marks/
│  │  │  ├─ subject-marks.html         ← from subject-marks.html (9KB)
│  │  │  ├─ subject-marks.css          ← from css/subject-marks.css (5KB)
│  │  │  └─ subject-marks.js           ← from js/subject-marks.js (13KB)
│  │  └─ sleep-saboteurs/
│  │     ├─ sleep-saboteurs.html       ← from sleep-saboteurs.html (8KB)
│  │     ├─ sleep-saboteurs.css        ← from css/sleep-saboteurs.css (17KB)
│  │     └─ sleep-saboteurs.js         ← from js/sleep-saboteurs-init.js
│  │
│  ├─ css/                             ← All styles organized (46 files → organized)
│  │  ├─ base/
│  │  │  ├─ reset.css                  ← new
│  │  │  ├─ variables.css              ← extract from main files
│  │  │  ├─ typography.css             ← extract from main files
│  │  │  └─ layout.css                 ← extract from main files
│  │  ├─ components/
│  │  │  ├─ buttons.css                ← extract from grind.css
│  │  │  ├─ forms.css                  ← extract from grind.css
│  │  │  ├─ cards.css                  ← extract from grind.css
│  │  │  ├─ modals.css                 ← extract from grind.css
│  │  │  ├─ navigation.css             ← from css/sideDrawer.css
│  │  │  ├─ notifications.css          ← from css/notification.css (1KB)
│  │  │  ├─ task-display.css           ← from css/task-display.css (1KB)
│  │  │  └─ text-expansion.css         ← from css/text-expansion.css (6KB)
│  │  ├─ pages/
│  │  │  ├─ grind.css                  ← split from grind.css (126KB → smaller chunks)
│  │  │  ├─ landing.css                ← extract from grind.css
│  │  │  ├─ workspace.css              ← from css/workspace.css (40KB)
│  │  │  ├─ study-spaces.css           ← from css/study-spaces.css (19KB)
│  │  │  ├─ academic-details.css       ← from css/academic-details.css (27KB)
│  │  │  ├─ extracted.css              ← from css/extracted.css (40KB)
│  │  │  └─ test-feedback.css          ← from css/test-feedback.css (16KB)
│  │  ├─ themes/
│  │  │  ├─ light.css                  ← extract from existing
│  │  │  ├─ dark.css                   ← extract from existing
│  │  │  └─ compact.css                ← from css/compact-style.css (4KB)
│  │  └─ main.css                      ← from styles/main.css (16KB) + imports
│  │
│  ├─ js/                              ← JavaScript organized (100 files → organized)
│  │  ├─ components/                   ← UI component logic
│  │  │  ├─ flashcard-manager.js       ← from js/flashcardManager.js (50KB)
│  │  │  ├─ calendar-manager.js        ← from js/calendarManager.js (43KB)
│  │  │  ├─ side-drawer.js             ← from js/sideDrawer.js (8KB)
│  │  │  ├─ priority-calculator.js     ← from priority-calculator.js (26KB)
│  │  │  ├─ task-display.js            ← combine task display logic
│  │  │  ├─ workspace-editor.js        ← combine workspace JS files
│  │  │  ├─ notification-system.js     ← combine notification logic
│  │  │  └─ common-header.js           ← from js/common-header.js (6KB)
│  │  │
│  │  ├─ features/                     ← Major feature modules
│  │  │  ├─ ai/
│  │  │  │  ├─ ai-researcher.js        ← from js/ai-researcher.js (101KB → split)
│  │  │  │  ├─ speech-recognition.js   ← from js/speech-recognition.js (81KB → split)
│  │  │  │  ├─ speech-synthesis.js     ← from js/speech-synthesis.js (26KB)
│  │  │  │  ├─ grind-speech.js         ← from js/grind-speech-synthesis.js (34KB)
│  │  │  │  └─ ai-latex-conversion.js  ← from js/ai-latex-conversion.js (1KB)
│  │  │  ├─ academic/
│  │  │  │  ├─ semester-management.js  ← from js/semester-management.js (62KB → split)
│  │  │  │  ├─ subject-management.js   ← from js/subject-management.js (24KB)
│  │  │  │  ├─ marks-tracking.js       ← from js/marks-tracking.js (0KB → implement)
│  │  │  │  ├─ timetable-analyzer.js   ← from js/timetableAnalyzer.js (9KB)
│  │  │  │  └─ academic-details.js     ← from js/academic-details.js (2KB)
│  │  │  ├─ study/
│  │  │  │  ├─ study-spaces-manager.js ← from js/studySpacesManager.js (57KB → split)
│  │  │  │  ├─ pomodoro-timer.js       ← from js/pomodoroTimer.js (35KB)
│  │  │  │  ├─ test-feedback.js        ← from js/test-feedback.js (53KB → split)
│  │  │  │  ├─ sleep-schedule.js       ← from js/sleepScheduleManager.js (3KB)
│  │  │  │  └─ energy-tracking.js      ← from js/energyLevels.js (2KB)
│  │  │  ├─ tasks/
│  │  │  │  ├─ task-manager.js         ← from js/tasksManager.js (9KB)
│  │  │  │  ├─ current-task.js         ← from js/currentTaskManager.js (12KB)
│  │  │  │  ├─ task-filters.js         ← from js/taskFilters.js (7KB)
│  │  │  │  ├─ task-attachments.js     ← from js/taskAttachments.js (27KB)
│  │  │  │  ├─ task-links.js           ← from js/taskLinks.js (24KB)
│  │  │  │  ├─ task-notes.js           ← from js/task-notes.js (25KB)
│  │  │  │  └─ priority-system.js      ← combine priority-related JS
│  │  │  └─ workspace/
│  │  │     ├─ workspace-core.js       ← from js/workspace-core.js (12KB)
│  │  │     ├─ workspace-formatting.js ← from js/workspace-formatting.js (5KB)
│  │  │     ├─ workspace-media.js      ← from js/workspace-media.js (10KB)
│  │  │     ├─ workspace-tables.js     ← from js/workspace-tables-links.js (11KB)
│  │  │     ├─ workspace-attachments.js← from js/workspace-attachments.js (25KB)
│  │  │     └─ workspace-ui.js         ← from js/workspace-ui.js (5KB)
│  │  │
│  │  ├─ utils/                        ← Helpers & APIs
│  │  │  ├─ api/
│  │  │  │  ├─ firebase-config.js      ← combine firebase JS files
│  │  │  │  ├─ firestore.js            ← from js/firestore.js (21KB)
│  │  │  │  ├─ google-drive-api.js     ← from js/googleDriveApi.js (72KB → split)
│  │  │  │  ├─ gemini-api.js           ← from js/gemini-api.js (4KB)
│  │  │  │  ├─ todoist-integration.js  ← from js/todoistIntegration.js (30KB)
│  │  │  │  └─ api-optimization.js     ← from js/api-optimization.js (17KB)
│  │  │  ├─ storage/
│  │  │  │  ├─ indexed-db.js           ← from js/indexedDB.js (0KB → implement)
│  │  │  │  ├─ storage-manager.js      ← from js/storageManager.js (2KB)
│  │  │  │  └─ cache-manager.js        ← from public/js/cacheManager.js (3KB)
│  │  │  ├─ helpers/
│  │  │  │  ├─ common.js               ← from js/common.js (2KB)
│  │  │  │  ├─ ui-utilities.js         ← from js/ui-utilities.js (5KB)
│  │  │  │  ├─ data-loader.js          ← from js/data-loader.js (0KB → implement)
│  │  │  │  └─ file-viewer.js          ← from js/fileViewer.js (17KB)
│  │  │  └─ services/
│  │  │     ├─ alarm-service.js        ← from js/alarm-service.js (21KB)
│  │  │     ├─ notification-service.js ← extract from various files
│  │  │     ├─ sync-service.js         ← from js/cross-tab-sync.js (11KB)
│  │  │     ├─ theme-manager.js        ← from js/themeManager.js (3KB)
│  │  │     └─ sound-manager.js        ← from js/soundManager.js (3KB)
│  │  │
│  │  └─ app.js                        ← Main entrypoint & router (new)
│  │
│  ├─ assets/                          ← Static resources
│  │  ├─ images/
│  │  │  └─ (move from assets/images/)
│  │  ├─ audio/
│  │  │  ├─ alarms/                    ← from alarm-sounds/
│  │  │  └─ notifications/             ← from sounds/
│  │  └─ fonts/
│  │
│  └─ data/                            ← Static data & configs
│     ├─ locations.json                ← from data/locations.json
│     ├─ schedule.json                 ← from data/schedule.json
│     └─ timetable.json                ← from data/timetable.json
│
├─ public/                             ← Build output & service worker
│  ├─ service-worker.js                ← from public/service-worker.js
│  └─ manifest.json                    ← new
│
├─ server/                             ← Backend (keep existing)
│  ├─ server.js                        ← from server.js
│  ├─ routes/                          ← from server/routes/
│  └─ data-storage.js                  ← from server/dataStorage.js
│
└─ config/                             ← Build & deployment configs
   ├─ firebase.json                    ← from firebase.json
   ├─ package.json                     ← from package.json
   └─ build-scripts/                   ← new
