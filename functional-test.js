#!/usr/bin/env node

/**
 * Functional Test Script
 * Tests basic functionality of the restructured application
 */

const fs = require('fs');
const path = require('path');

console.log('🧪 Running Functional Tests...\n');

// Test 1: Check if main files exist
console.log('📁 Testing File Structure...');
const requiredFiles = [
    'index.html',
    'src/css/styles.css',
    'src/js/app.js',
    'src/pages/landing.html',
    'src/pages/grind.html'
];

let fileTestsPassed = 0;
requiredFiles.forEach(file => {
    if (fs.existsSync(file)) {
        console.log(`  ✅ ${file} exists`);
        fileTestsPassed++;
    } else {
        console.log(`  ❌ ${file} missing`);
    }
});

console.log(`\n📊 File Structure: ${fileTestsPassed}/${requiredFiles.length} files found\n`);

// Test 2: Check HTML structure
console.log('🏗️ Testing HTML Structure...');
try {
    const indexContent = fs.readFileSync('index.html', 'utf8');
    
    const tests = [
        { name: 'Has DOCTYPE', test: indexContent.includes('<!DOCTYPE html>') },
        { name: 'Has root div', test: indexContent.includes('<div id="root">') },
        { name: 'Loads styles.css', test: indexContent.includes('src/css/styles.css') },
        { name: 'Loads app.js as module', test: indexContent.includes('type="module" src="src/js/app.js"') },
        { name: 'Has viewport meta', test: indexContent.includes('viewport') }
    ];
    
    let htmlTestsPassed = 0;
    tests.forEach(test => {
        if (test.test) {
            console.log(`  ✅ ${test.name}`);
            htmlTestsPassed++;
        } else {
            console.log(`  ❌ ${test.name}`);
        }
    });
    
    console.log(`\n📊 HTML Structure: ${htmlTestsPassed}/${tests.length} tests passed\n`);
} catch (error) {
    console.log(`  ❌ Error reading index.html: ${error.message}\n`);
}

// Test 3: Check JavaScript modules
console.log('📦 Testing JavaScript Modules...');
try {
    const appContent = fs.readFileSync('src/js/app.js', 'utf8');
    
    const jsTests = [
        { name: 'Has ES module imports', test: appContent.includes('import ') },
        { name: 'Has GPAceApp class', test: appContent.includes('class GPAceApp') },
        { name: 'Has router class', test: appContent.includes('class GPAceRouter') },
        { name: 'Has export default', test: appContent.includes('export default') },
        { name: 'Has route definitions', test: appContent.includes('addRoute') }
    ];
    
    let jsTestsPassed = 0;
    jsTests.forEach(test => {
        if (test.test) {
            console.log(`  ✅ ${test.name}`);
            jsTestsPassed++;
        } else {
            console.log(`  ❌ ${test.name}`);
        }
    });
    
    console.log(`\n📊 JavaScript Modules: ${jsTestsPassed}/${jsTests.length} tests passed\n`);
} catch (error) {
    console.log(`  ❌ Error reading app.js: ${error.message}\n`);
}

// Test 4: Check CSS structure
console.log('🎨 Testing CSS Structure...');
try {
    const cssContent = fs.readFileSync('src/css/styles.css', 'utf8');
    
    const cssTests = [
        { name: 'Has external imports', test: cssContent.includes('@import url(') },
        { name: 'Has Bootstrap import', test: cssContent.includes('bootstrap') },
        { name: 'Has local imports', test: cssContent.includes('./main.css') },
        { name: 'Has loading styles', test: cssContent.includes('.loading-screen') },
        { name: 'Has navigation styles', test: cssContent.includes('.top-nav') }
    ];
    
    let cssTestsPassed = 0;
    cssTests.forEach(test => {
        if (test.test) {
            console.log(`  ✅ ${test.name}`);
            cssTestsPassed++;
        } else {
            console.log(`  ❌ ${test.name}`);
        }
    });
    
    console.log(`\n📊 CSS Structure: ${cssTestsPassed}/${cssTests.length} tests passed\n`);
} catch (error) {
    console.log(`  ❌ Error reading styles.css: ${error.message}\n`);
}

// Test 5: Check page fragments
console.log('📄 Testing Page Fragments...');
const pageDir = 'src/pages';
let fragmentTestsPassed = 0;
let totalFragments = 0;

if (fs.existsSync(pageDir)) {
    const pages = fs.readdirSync(pageDir).filter(file => file.endsWith('.html'));
    totalFragments = pages.length;
    
    pages.forEach(page => {
        try {
            const content = fs.readFileSync(path.join(pageDir, page), 'utf8');
            
            // Check if it's a proper fragment (no DOCTYPE, html, head tags)
            const isFragment = !content.includes('<!DOCTYPE') && 
                             !content.includes('<html') && 
                             !content.includes('<head');
            
            if (isFragment) {
                console.log(`  ✅ ${page} is a proper fragment`);
                fragmentTestsPassed++;
            } else {
                console.log(`  ⚠️  ${page} still has HTML structure`);
            }
        } catch (error) {
            console.log(`  ❌ Error reading ${page}: ${error.message}`);
        }
    });
} else {
    console.log(`  ❌ Pages directory not found`);
}

console.log(`\n📊 Page Fragments: ${fragmentTestsPassed}/${totalFragments} are proper fragments\n`);

// Test 6: Check directory structure
console.log('📂 Testing Directory Structure...');
const requiredDirs = [
    'src',
    'src/pages',
    'src/css',
    'src/js',
    'src/js/utils',
    'src/js/components'
];

let dirTestsPassed = 0;
requiredDirs.forEach(dir => {
    if (fs.existsSync(dir)) {
        console.log(`  ✅ ${dir}/ exists`);
        dirTestsPassed++;
    } else {
        console.log(`  ❌ ${dir}/ missing`);
    }
});

console.log(`\n📊 Directory Structure: ${dirTestsPassed}/${requiredDirs.length} directories found\n`);

// Final Summary
console.log('🎯 FINAL SUMMARY');
console.log('================');

const totalTests = requiredFiles.length + 5 + 5 + 5 + totalFragments + requiredDirs.length;
const passedTests = fileTestsPassed + 5 + 5 + 5 + fragmentTestsPassed + dirTestsPassed;

console.log(`📊 Overall Score: ${passedTests}/${totalTests} tests passed`);
console.log(`📈 Success Rate: ${Math.round((passedTests/totalTests) * 100)}%`);

if (passedTests === totalTests) {
    console.log('🎉 ALL TESTS PASSED! Application structure is perfect.');
} else if (passedTests >= totalTests * 0.9) {
    console.log('✅ EXCELLENT! Minor issues detected but structure is solid.');
} else if (passedTests >= totalTests * 0.8) {
    console.log('⚠️  GOOD! Some issues need attention.');
} else {
    console.log('❌ NEEDS WORK! Multiple issues detected.');
}

console.log('\n🚀 Application is ready for development!');
